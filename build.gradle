plugins {
    id 'java'
}

targetCompatibility = '1.8'
sourceCompatibility = '1.8'

repositories {
    mavenCentral()
}

dependencies {
    implementation 'net.portswigger.burp.extender:burp-extender-api:2.3'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.13.3'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.3'
    implementation 'com.google.re2j:re2j:1.7'
}

task fatJar(type: Jar) {
    baseName = project.name + '-all'
    from { configurations.compileClasspath.collect { it.isDirectory() ? it : zipTree(it) } }
    with jar
    duplicatesStrategy = 'include'
}
