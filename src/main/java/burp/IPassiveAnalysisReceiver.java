package burp;

/**
 * 被动分析接收器接口
 * 各个功能Tab实现此接口来接收被动分析的数据
 */
public interface IPassiveAnalysisReceiver {
    
    /**
     * 处理被动分析发现的API路径
     * @param apiPath API路径
     * @param sourceUrl 来源URL
     * @param method HTTP方法（如果能确定）
     */
    default void onApiPathDiscovered(String apiPath, String sourceUrl, String method) {
        // 默认空实现，子类可选择性实现
    }
    
    /**
     * 处理被动分析发现的敏感信息
     * @param type 敏感信息类型
     * @param value 敏感信息值
     * @param sourceUrl 来源URL
     * @param context 上下文信息
     */
    default void onSensitiveInfoDiscovered(String type, String value, String sourceUrl, String context) {
        // 默认空实现，子类可选择性实现
    }
    
    /**
     * 处理被动分析发现的JavaScript文件
     * @param jsUrl JavaScript文件URL
     * @param sourceUrl 来源URL
     */
    default void onJavaScriptFileDiscovered(String jsUrl, String sourceUrl) {
        // 默认空实现，子类可选择性实现
    }
    
    /**
     * 处理被动分析发现的域名
     * @param domain 域名
     * @param sourceUrl 来源URL
     */
    default void onDomainDiscovered(String domain, String sourceUrl) {
        // 默认空实现，子类可选择性实现
    }
    
    /**
     * 获取接收器名称（用于日志和调试）
     * @return 接收器名称
     */
    String getReceiverName();
    
    /**
     * 检查接收器是否启用
     * @return true如果启用，false如果禁用
     */
    default boolean isEnabled() {
        return true;
    }
}
