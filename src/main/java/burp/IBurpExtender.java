package burp;

import java.awt.Component;
import java.net.URL;
import java.util.List;

/**
 * Minimal Burp Suite interface definitions for compilation only.
 * The real interfaces will be provided by Burp Suite at runtime.
 */

public interface IBurpExtender {
    void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks);
}

interface ITab {
    String getTabCaption();
    Component getUiComponent();
}

interface IHttpListener {
    void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo);
}

interface IBurpExtenderCallbacks {
    IExtensionHelpers getHelpers();
    void setExtensionName(String name);
    void customizeUiComponent(Component component);
    void addSuiteTab(ITab tab);
    void registerHttpListener(IHttpListener listener);
    void printOutput(String output);
    void printError(String error);
    IHttpRequestResponse makeHttpRequest(IHttpService httpService, byte[] request);
    void sendToRepeater(String host, int port, boolean useHttps, byte[] request);

    // Burp原生HTTP查看器组件
    IMessageEditor createMessageEditor(IMessageEditorController controller, boolean editable);
    ITextEditor createTextEditor();
}

interface IExtensionHelpers {
    byte[] buildHttpRequest(URL url);
    IHttpService buildHttpService(String host, int port, String protocol);
    IResponseInfo analyzeResponse(byte[] response);
    IRequestInfo analyzeRequest(IHttpRequestResponse request);
}

interface IHttpRequestResponse {
    byte[] getResponse();
    byte[] getRequest();
    IHttpService getHttpService();
    void setResponse(byte[] response);
    void setRequest(byte[] request);
    void setHttpService(IHttpService httpService);
}

interface IRequestInfo {
    URL getUrl();
    String getMethod();
    List<String> getHeaders();
    int getBodyOffset();
    byte getContentType();
}

interface IResponseInfo {
    int getStatusCode();
    List<String> getHeaders();
    String getMimeType();
    int getBodyOffset();
}

interface IHttpService {
    String getHost();
    int getPort();
    String getProtocol();
}

// Burp原生HTTP查看器相关接口
interface IMessageEditor {
    Component getComponent();
    void setMessage(byte[] message, boolean isRequest);
    byte[] getMessage();
    boolean isMessageModified();
    byte[] getSelectedData();
}

interface IMessageEditorController {
    IHttpService getHttpService();
    byte[] getRequest();
    byte[] getResponse();
}

interface ITextEditor {
    Component getComponent();
    void setText(byte[] text);
    byte[] getText();
    boolean isTextModified();
    byte[] getSelectedText();
}