package burp;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Set;

/**
 * 被动分析控制面板
 * 提供被动分析功能的配置和控制界面
 */
public class PassiveAnalysisControlPanel extends JPanel {
    
    private final IBurpExtenderCallbacks callbacks;
    private final PassiveAnalysisConfig config;
    private final PassiveAnalysisProcessor processor;
    
    // 主要控制组件
    private JCheckBox globalEnabledCheckBox;
    private JCheckBox apiDiscoveryCheckBox;
    private JCheckBox sensitiveInfoCheckBox;
    private JCheckBox jsAnalysisCheckBox;
    private JCheckBox domainCollectionCheckBox;
    
    // 过滤控制组件
    private JCheckBox domainFilterCheckBox;
    private JTextArea includeDomainsArea;
    private JTextArea excludeDomainsArea;
    private JTextArea excludeExtensionsArea;
    
    // 性能控制组件
    private JSpinner maxUrlsSpinner;
    private JSpinner maxResponseSizeSpinner;
    private JCheckBox asyncProcessingCheckBox;
    
    // 统计显示组件
    private JLabel totalProcessedLabel;
    private JLabel apiPathsFoundLabel;
    private JLabel sensitiveInfoFoundLabel;
    private JLabel jsFilesFoundLabel;
    private JLabel processedUrlsLabel;
    
    // 控制按钮
    private JButton resetStatsButton;
    private JButton clearProcessedUrlsButton;
    private JButton exportConfigButton;
    private JButton importConfigButton;
    
    public PassiveAnalysisControlPanel(IBurpExtenderCallbacks callbacks, 
                                     PassiveAnalysisConfig config, 
                                     PassiveAnalysisProcessor processor) {
        this.callbacks = callbacks;
        this.config = config;
        this.processor = processor;
        
        initializeComponents();
        layoutComponents();
        setupEventHandlers();
        updateControlsState();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        // 主要控制组件
        globalEnabledCheckBox = new JCheckBox("启用被动分析", config.isGlobalEnabled());
        apiDiscoveryCheckBox = new JCheckBox("API路径发现", config.isApiDiscoveryEnabled());
        sensitiveInfoCheckBox = new JCheckBox("敏感信息提取", config.isSensitiveInfoEnabled());
        jsAnalysisCheckBox = new JCheckBox("JavaScript文件分析", config.isJsAnalysisEnabled());
        domainCollectionCheckBox = new JCheckBox("域名收集", config.isDomainCollectionEnabled());
        
        // 过滤控制组件
        domainFilterCheckBox = new JCheckBox("启用域名过滤", config.isDomainFilterEnabled());
        includeDomainsArea = new JTextArea(3, 20);
        excludeDomainsArea = new JTextArea(3, 20);
        excludeExtensionsArea = new JTextArea(2, 20);
        
        // 设置文本区域内容
        includeDomainsArea.setText(String.join("\n", config.getIncludeDomains()));
        excludeDomainsArea.setText(String.join("\n", config.getExcludeDomains()));
        excludeExtensionsArea.setText(String.join("\n", config.getExcludeExtensions()));
        
        // 性能控制组件
        maxUrlsSpinner = new JSpinner(new SpinnerNumberModel(config.getMaxUrlsPerMinute(), 1, 1000, 10));
        maxResponseSizeSpinner = new JSpinner(new SpinnerNumberModel(config.getMaxResponseSize() / 1024, 1, 10240, 100));
        asyncProcessingCheckBox = new JCheckBox("异步处理", config.isAsyncProcessing());
        
        // 统计显示组件
        totalProcessedLabel = new JLabel("0");
        apiPathsFoundLabel = new JLabel("0");
        sensitiveInfoFoundLabel = new JLabel("0");
        jsFilesFoundLabel = new JLabel("0");
        processedUrlsLabel = new JLabel("0");
        
        // 控制按钮
        resetStatsButton = new JButton("重置统计");
        clearProcessedUrlsButton = new JButton("清空已处理URL");
        exportConfigButton = new JButton("导出配置");
        importConfigButton = new JButton("导入配置");
    }
    
    /**
     * 布局组件
     */
    private void layoutComponents() {
        setLayout(new BorderLayout());
        
        // 创建主面板
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));
        
        // 功能开关面板
        JPanel switchPanel = createSwitchPanel();
        mainPanel.add(switchPanel);
        
        // 过滤配置面板
        JPanel filterPanel = createFilterPanel();
        mainPanel.add(filterPanel);
        
        // 性能配置面板
        JPanel performancePanel = createPerformancePanel();
        mainPanel.add(performancePanel);
        
        // 统计信息面板
        JPanel statsPanel = createStatsPanel();
        mainPanel.add(statsPanel);
        
        // 控制按钮面板
        JPanel controlPanel = createControlPanel();
        mainPanel.add(controlPanel);
        
        // 添加到滚动面板
        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        
        add(scrollPane, BorderLayout.CENTER);
    }
    
    /**
     * 创建功能开关面板
     */
    private JPanel createSwitchPanel() {
        JPanel panel = new JPanel(new GridLayout(3, 2, 5, 5));
        panel.setBorder(BorderFactory.createTitledBorder("功能开关"));
        
        panel.add(globalEnabledCheckBox);
        panel.add(apiDiscoveryCheckBox);
        panel.add(sensitiveInfoCheckBox);
        panel.add(jsAnalysisCheckBox);
        panel.add(domainCollectionCheckBox);
        panel.add(new JLabel()); // 占位符
        
        return panel;
    }
    
    /**
     * 创建过滤配置面板
     */
    private JPanel createFilterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("过滤配置"));
        
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        topPanel.add(domainFilterCheckBox);
        panel.add(topPanel, BorderLayout.NORTH);
        
        JPanel centerPanel = new JPanel(new GridLayout(1, 3, 10, 5));
        
        // 包含域名
        JPanel includePanel = new JPanel(new BorderLayout());
        includePanel.setBorder(BorderFactory.createTitledBorder("包含域名 (每行一个)"));
        includePanel.add(new JScrollPane(includeDomainsArea), BorderLayout.CENTER);
        centerPanel.add(includePanel);
        
        // 排除域名
        JPanel excludePanel = new JPanel(new BorderLayout());
        excludePanel.setBorder(BorderFactory.createTitledBorder("排除域名 (每行一个)"));
        excludePanel.add(new JScrollPane(excludeDomainsArea), BorderLayout.CENTER);
        centerPanel.add(excludePanel);
        
        // 排除扩展名
        JPanel extensionPanel = new JPanel(new BorderLayout());
        extensionPanel.setBorder(BorderFactory.createTitledBorder("排除扩展名 (每行一个)"));
        extensionPanel.add(new JScrollPane(excludeExtensionsArea), BorderLayout.CENTER);
        centerPanel.add(extensionPanel);
        
        panel.add(centerPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建性能配置面板
     */
    private JPanel createPerformancePanel() {
        JPanel panel = new JPanel(new GridLayout(2, 3, 10, 5));
        panel.setBorder(BorderFactory.createTitledBorder("性能配置"));
        
        panel.add(new JLabel("最大URL/分钟:"));
        panel.add(maxUrlsSpinner);
        panel.add(asyncProcessingCheckBox);
        
        panel.add(new JLabel("最大响应大小(KB):"));
        panel.add(maxResponseSizeSpinner);
        panel.add(new JLabel()); // 占位符
        
        return panel;
    }
    
    /**
     * 创建统计信息面板
     */
    private JPanel createStatsPanel() {
        JPanel panel = new JPanel(new GridLayout(3, 4, 5, 5));
        panel.setBorder(BorderFactory.createTitledBorder("统计信息"));
        
        panel.add(new JLabel("总处理数:"));
        panel.add(totalProcessedLabel);
        panel.add(new JLabel("API路径:"));
        panel.add(apiPathsFoundLabel);
        
        panel.add(new JLabel("敏感信息:"));
        panel.add(sensitiveInfoFoundLabel);
        panel.add(new JLabel("JS文件:"));
        panel.add(jsFilesFoundLabel);
        
        panel.add(new JLabel("已处理URL:"));
        panel.add(processedUrlsLabel);
        panel.add(new JLabel());
        panel.add(new JLabel());
        
        return panel;
    }
    
    /**
     * 创建控制按钮面板
     */
    private JPanel createControlPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        panel.setBorder(BorderFactory.createTitledBorder("控制操作"));
        
        panel.add(resetStatsButton);
        panel.add(clearProcessedUrlsButton);
        panel.add(exportConfigButton);
        panel.add(importConfigButton);
        
        return panel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 功能开关事件
        globalEnabledCheckBox.addActionListener(e -> {
            config.setGlobalEnabled(globalEnabledCheckBox.isSelected());
            updateControlsState();
            callbacks.printOutput("被动分析全局开关: " + (globalEnabledCheckBox.isSelected() ? "启用" : "禁用"));
        });
        
        apiDiscoveryCheckBox.addActionListener(e -> {
            config.setApiDiscoveryEnabled(apiDiscoveryCheckBox.isSelected());
            callbacks.printOutput("API路径发现: " + (apiDiscoveryCheckBox.isSelected() ? "启用" : "禁用"));
        });
        
        sensitiveInfoCheckBox.addActionListener(e -> {
            config.setSensitiveInfoEnabled(sensitiveInfoCheckBox.isSelected());
            callbacks.printOutput("敏感信息提取: " + (sensitiveInfoCheckBox.isSelected() ? "启用" : "禁用"));
        });
        
        jsAnalysisCheckBox.addActionListener(e -> {
            config.setJsAnalysisEnabled(jsAnalysisCheckBox.isSelected());
            callbacks.printOutput("JavaScript文件分析: " + (jsAnalysisCheckBox.isSelected() ? "启用" : "禁用"));
        });
        
        domainCollectionCheckBox.addActionListener(e -> {
            config.setDomainCollectionEnabled(domainCollectionCheckBox.isSelected());
            callbacks.printOutput("域名收集: " + (domainCollectionCheckBox.isSelected() ? "启用" : "禁用"));
        });
        
        // 过滤配置事件
        domainFilterCheckBox.addActionListener(e -> {
            config.setDomainFilterEnabled(domainFilterCheckBox.isSelected());
            updateControlsState();
            callbacks.printOutput("域名过滤: " + (domainFilterCheckBox.isSelected() ? "启用" : "禁用"));
        });
        
        // 性能配置事件
        maxUrlsSpinner.addChangeListener(e -> {
            config.setMaxUrlsPerMinute((Integer) maxUrlsSpinner.getValue());
        });
        
        maxResponseSizeSpinner.addChangeListener(e -> {
            config.setMaxResponseSize((Integer) maxResponseSizeSpinner.getValue() * 1024);
        });
        
        asyncProcessingCheckBox.addActionListener(e -> {
            config.setAsyncProcessing(asyncProcessingCheckBox.isSelected());
            callbacks.printOutput("异步处理: " + (asyncProcessingCheckBox.isSelected() ? "启用" : "禁用"));
        });
        
        // 控制按钮事件
        resetStatsButton.addActionListener(e -> {
            config.resetStatistics();
            updateStatistics();
            callbacks.printOutput("统计信息已重置");
        });
        
        clearProcessedUrlsButton.addActionListener(e -> {
            config.clearProcessedUrls();
            updateStatistics();
            callbacks.printOutput("已处理URL列表已清空");
        });
        
        exportConfigButton.addActionListener(e -> {
            String configSummary = config.getConfigSummary();
            callbacks.printOutput("当前配置:\n" + configSummary);
            JOptionPane.showMessageDialog(this, configSummary, "配置信息", JOptionPane.INFORMATION_MESSAGE);
        });
        
        importConfigButton.addActionListener(e -> {
            JOptionPane.showMessageDialog(this, "配置导入功能待实现", "提示", JOptionPane.INFORMATION_MESSAGE);
        });
    }
    
    /**
     * 更新控件状态
     */
    private void updateControlsState() {
        boolean globalEnabled = config.isGlobalEnabled();
        
        // 更新功能开关状态
        apiDiscoveryCheckBox.setEnabled(globalEnabled);
        sensitiveInfoCheckBox.setEnabled(globalEnabled);
        jsAnalysisCheckBox.setEnabled(globalEnabled);
        domainCollectionCheckBox.setEnabled(globalEnabled);
        
        // 更新过滤配置状态
        boolean filterEnabled = config.isDomainFilterEnabled() && globalEnabled;
        includeDomainsArea.setEnabled(filterEnabled);
        excludeDomainsArea.setEnabled(filterEnabled);
        excludeExtensionsArea.setEnabled(filterEnabled);
        
        // 更新统计信息
        updateStatistics();
    }
    
    /**
     * 更新统计信息
     */
    public void updateStatistics() {
        SwingUtilities.invokeLater(() -> {
            totalProcessedLabel.setText(String.valueOf(config.getTotalProcessed()));
            apiPathsFoundLabel.setText(String.valueOf(config.getApiPathsFound()));
            sensitiveInfoFoundLabel.setText(String.valueOf(config.getSensitiveInfoFound()));
            jsFilesFoundLabel.setText(String.valueOf(config.getJsFilesFound()));
            processedUrlsLabel.setText(String.valueOf(config.getTotalProcessed())); // 使用总处理数作为代理
        });
    }
    
    /**
     * 应用过滤配置
     */
    public void applyFilterConfig() {
        // 更新包含域名
        config.clearIncludeDomains();
        String[] includeLines = includeDomainsArea.getText().split("\n");
        for (String line : includeLines) {
            String domain = line.trim();
            if (!domain.isEmpty()) {
                config.addIncludeDomain(domain);
            }
        }
        
        // 更新排除域名
        config.clearExcludeDomains();
        String[] excludeLines = excludeDomainsArea.getText().split("\n");
        for (String line : excludeLines) {
            String domain = line.trim();
            if (!domain.isEmpty()) {
                config.addExcludeDomain(domain);
            }
        }
        
        // 更新排除扩展名
        Set<String> currentExtensions = config.getExcludeExtensions();
        config.getExcludeExtensions().clear();
        String[] extensionLines = excludeExtensionsArea.getText().split("\n");
        for (String line : extensionLines) {
            String extension = line.trim();
            if (!extension.isEmpty()) {
                config.addExcludeExtension(extension);
            }
        }
        
        callbacks.printOutput("过滤配置已更新");
    }
}
