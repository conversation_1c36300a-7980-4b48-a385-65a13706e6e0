package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * API测试Tab - 提供API端点的批量测试功能
 */
public class ApiTestingTab {
    
    private IBurpExtenderCallbacks callbacks;
    private ApiScanMainTab mainTab;
    
    // UI组件
    private JPanel mainPanel;
    private JSplitPane splitPane;
    private JLabel statusLabel;
    private JProgressBar progressBar;

    // ✅ 集成JsRouteScan风格的测试结果组件
    private JsRouteScanStyleTestResultTab testResultTab;
    
    // 测试配置组件
    private JComboBox<String> hostCombo;
    private JComboBox<String> methodCombo;
    private JTextField threadsField;
    private JTextField timeoutField;
    private JTextArea customHeadersArea;
    private JButton startTestButton;
    private JButton stopTestButton;

    // 自定义API基础路径组件
    private JTextField customApiPathField;
    private JLabel currentBasePathLabel;
    private String customBasePath; // 当前设置的基础路径
    
    // 测试数据
    private List<ApiTestResult> testResults;
    private ExecutorService testExecutor;
    private volatile boolean isTestRunning = false;
    
    // ✅ 添加原子计数器来正确跟踪完成的测试数量
    private AtomicInteger completedTestCount = new AtomicInteger(0);
    private volatile int totalTestCount = 0;
    
    // ✅ 表格列定义已移至JsRouteScan风格的测试结果Tab中
    
    /**
     * 手动解析HTTP响应状态码
     * @param responseBytes HTTP响应字节数组
     * @return 状态码，如果解析失败返回-1
     */
    private int parseHttpStatusCode(byte[] responseBytes) {
        try {
            String responseStr = new String(responseBytes);
            String[] lines = responseStr.split("\\r?\\n");
            if (lines.length > 0 && lines[0].startsWith("HTTP/")) {
                // 解析状态行，如 "HTTP/1.1 200 OK"
                String[] statusParts = lines[0].split(" ");
                if (statusParts.length >= 2) {
                    return Integer.parseInt(statusParts[1]);
                }
            }
        } catch (Exception e) {
            callbacks.printOutput("❌ 状态码解析失败: " + e.getMessage());
        }
        return -1;
    }
    
    public ApiTestingTab(IBurpExtenderCallbacks callbacks, ApiScanMainTab mainTab) {
        this.callbacks = callbacks;
        this.mainTab = mainTab;
        // ✅ 使用线程安全的列表，避免并发访问问题
        this.testResults = Collections.synchronizedList(new ArrayList<>());
        this.customBasePath = null; // 初始化为空

        // ✅ 初始化JsRouteScan风格的测试结果Tab
        this.testResultTab = new JsRouteScanStyleTestResultTab(callbacks, mainTab);

        initializeUI();

        // 初始化时刷新主机列表
        refreshHostList();
    }
    
    /**
     * 初始化用户界面 - 左右分列布局
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());

        // ✅ 创建左右分列的主要内容区域
        splitPane = createTwoColumnLayout();
        mainPanel.add(splitPane, BorderLayout.CENTER);

        // 创建底部状态栏
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建配置面板 - 适合左侧面板的垂直布局
     */
    private JPanel createConfigPanel() {
        JPanel configPanel = new JPanel();
        configPanel.setLayout(new BoxLayout(configPanel, BoxLayout.Y_AXIS));

        // ✅ 基本配置区域 - 垂直排列，适合左侧面板
        JPanel basicConfigPanel = new JPanel();
        basicConfigPanel.setLayout(new BoxLayout(basicConfigPanel, BoxLayout.Y_AXIS));
        basicConfigPanel.setBorder(BorderFactory.createTitledBorder("基本配置"));

        // 目标主机
        JPanel hostPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        hostPanel.add(new JLabel("目标主机:"));
        hostCombo = new JComboBox<>();
        hostCombo.addItem("全部主机");
        hostCombo.setPreferredSize(new Dimension(180, 25));
        hostPanel.add(hostCombo);
        basicConfigPanel.add(hostPanel);

        // HTTP方法
        JPanel methodPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        methodPanel.add(new JLabel("HTTP方法:"));
        methodCombo = new JComboBox<>(new String[]{"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"});
        methodCombo.setPreferredSize(new Dimension(100, 25));
        methodPanel.add(methodCombo);
        basicConfigPanel.add(methodPanel);

        // 并发线程
        JPanel threadsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        threadsPanel.add(new JLabel("并发线程:"));
        threadsField = new JTextField("5", 8);
        threadsPanel.add(threadsField);
        basicConfigPanel.add(threadsPanel);

        // 超时设置
        JPanel timeoutPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        timeoutPanel.add(new JLabel("超时(秒):"));
        timeoutField = new JTextField("30", 8);
        timeoutPanel.add(timeoutField);
        basicConfigPanel.add(timeoutPanel);

        configPanel.add(basicConfigPanel);
        
        // ✅ 自定义HTTP头区域 - 适合左侧面板
        JPanel headersPanel = new JPanel(new BorderLayout());
        headersPanel.setBorder(BorderFactory.createTitledBorder("自定义HTTP头"));

        customHeadersArea = new JTextArea(4, 20);
        customHeadersArea.setToolTipText("每行一个头部，格式: Header-Name: Value");
        customHeadersArea.setText("User-Agent: Burp API Tester\nAccept: application/json, */*");
        JScrollPane headersScrollPane = new JScrollPane(customHeadersArea);
        headersScrollPane.setPreferredSize(new Dimension(250, 100));
        headersPanel.add(headersScrollPane, BorderLayout.CENTER);

        configPanel.add(headersPanel);

        // ✅ 自定义API基础路径区域 - 适合左侧面板
        JPanel customApiPanel = new JPanel();
        customApiPanel.setLayout(new BoxLayout(customApiPanel, BoxLayout.Y_AXIS));
        customApiPanel.setBorder(BorderFactory.createTitledBorder("自定义API基础路径"));

        // 基础路径输入
        JPanel pathInputPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        pathInputPanel.add(new JLabel("基础路径:"));
        customApiPathField = new JTextField(15);
        customApiPathField.setToolTipText("输入基础URL或基础路径");
        pathInputPanel.add(customApiPathField);
        customApiPanel.add(pathInputPanel);

        // 设置按钮
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        JButton setBasePathButton = new JButton("设置基础路径");
        setBasePathButton.addActionListener(e -> setCustomBasePath());
        buttonPanel.add(setBasePathButton);
        customApiPanel.add(buttonPanel);

        // 当前基础路径显示
        currentBasePathLabel = new JLabel("当前基础路径: 未设置");
        currentBasePathLabel.setForeground(Color.BLUE);
        JPanel basePathInfoPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        basePathInfoPanel.add(currentBasePathLabel);
        customApiPanel.add(basePathInfoPanel);

        configPanel.add(customApiPanel);

        // ✅ 控制按钮区域 - 适合左侧面板的垂直布局
        JPanel controlPanel = new JPanel();
        controlPanel.setLayout(new BoxLayout(controlPanel, BoxLayout.Y_AXIS));
        controlPanel.setBorder(BorderFactory.createTitledBorder("测试控制"));

        // 开始/停止按钮
        JPanel testButtonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        startTestButton = new JButton("🚀 开始测试");
        startTestButton.addActionListener(e -> startApiTesting());
        testButtonPanel.add(startTestButton);

        stopTestButton = new JButton("停止测试");
        stopTestButton.setEnabled(false);
        stopTestButton.addActionListener(e -> stopApiTesting());
        testButtonPanel.add(stopTestButton);
        controlPanel.add(testButtonPanel);

        // 刷新按钮
        JPanel refreshButtonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        JButton refreshHostsButton = new JButton("刷新主机");
        refreshHostsButton.addActionListener(e -> refreshHostList());
        refreshButtonPanel.add(refreshHostsButton);
        controlPanel.add(refreshButtonPanel);

        configPanel.add(controlPanel);

        return configPanel;
    }
    
    /**
     * 创建左右分列布局 - 左边配置，右边结果
     */
    private JSplitPane createTwoColumnLayout() {
        // ✅ 创建水平分割面板 - 左右分列
        JSplitPane horizontalSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);

        // ✅ 左侧：测试配置面板 (1/3宽度)
        JPanel leftPanel = createLeftConfigPanel();
        horizontalSplitPane.setLeftComponent(leftPanel);

        // ✅ 右侧：测试结果面板 (2/3宽度)
        JPanel rightPanel = createRightResultPanel();
        horizontalSplitPane.setRightComponent(rightPanel);

        // ✅ 设置分割比例：左边1/3，右边2/3
        horizontalSplitPane.setResizeWeight(0.33);
        horizontalSplitPane.setDividerLocation(0.33);

        return horizontalSplitPane;
    }

    /**
     * 创建左侧配置面板
     */
    private JPanel createLeftConfigPanel() {
        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.setBorder(BorderFactory.createTitledBorder("测试配置"));

        // ✅ 使用原有的配置面板创建方法
        JPanel configContent = createConfigPanel();
        leftPanel.add(configContent, BorderLayout.CENTER);

        return leftPanel;
    }

    /**
     * 创建右侧结果面板
     */
    private JPanel createRightResultPanel() {
        JPanel rightPanel = new JPanel(new BorderLayout());
        rightPanel.setBorder(BorderFactory.createTitledBorder("测试结果"));

        // ✅ 直接使用JsRouteScan风格的测试结果Tab面板
        JPanel testResultPanel = testResultTab.getPanel();
        rightPanel.add(testResultPanel, BorderLayout.CENTER);

        return rightPanel;
    }
    
    /**
     * 创建状态栏
     */
    private JPanel createStatusPanel() {
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        // 左侧状态信息
        statusLabel = new JLabel("就绪 - 等待开始测试");
        statusPanel.add(statusLabel, BorderLayout.WEST);
        
        // 右侧进度条
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setString("0%");
        progressBar.setPreferredSize(new Dimension(200, 20));
        statusPanel.add(progressBar, BorderLayout.EAST);
        
        return statusPanel;
    }
    
    // ✅ 右键菜单功能已集成到JsRouteScan风格的测试结果Tab中，无需单独实现
    
    /**
     * 开始API测试
     */
    private void startApiTesting() {
        if (isTestRunning) {
            return;
        }
        
        // 获取要测试的路径
        List<RouteContent> routesToTest = getRoutesToTest();
        if (routesToTest.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "没有找到可测试的API路径，请先进行路径发现");
            return;
        }
        
        // 获取配置
        String selectedMethod = (String) methodCombo.getSelectedItem();
        int threadCount;
        int timeout;
        
        try {
            threadCount = Integer.parseInt(threadsField.getText().trim());
            timeout = Integer.parseInt(timeoutField.getText().trim());
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(mainPanel, "请输入有效的数字");
            return;
        }
        
        // 启动测试
        isTestRunning = true;
        startTestButton.setEnabled(false);
        stopTestButton.setEnabled(true);
        
        // ✅ 重置计数器和清空之前的结果
        completedTestCount.set(0);
        totalTestCount = routesToTest.size();
        testResults.clear();
        
        testExecutor = Executors.newFixedThreadPool(threadCount);
        
        callbacks.printOutput("开始API测试...");

        // 显示基础路径使用情况
        if (customBasePath != null && !customBasePath.trim().isEmpty()) {
            callbacks.printOutput("使用自定义基础路径: " + customBasePath);
            if (customBasePath.startsWith("http://") || customBasePath.startsWith("https://")) {
                callbacks.printOutput("基础路径模式: 完整URL - 将直接使用基础路径+API路径");
            } else {
                callbacks.printOutput("基础路径模式: 相对路径 - 将使用来源主机+基础路径+API路径");
            }
        } else {
            callbacks.printOutput("使用原始发现的路径进行测试");
        }

        callbacks.printOutput("测试路径数量: " + routesToTest.size());
        callbacks.printOutput("HTTP方法: " + selectedMethod);
        callbacks.printOutput("并发线程: " + threadCount);
        callbacks.printOutput("超时时间: " + timeout + "秒");
        callbacks.printOutput("----------------------------------------");
        
        // 重置进度条
        progressBar.setMaximum(routesToTest.size());
        progressBar.setValue(0);
        progressBar.setString("0%");
        
        // 提交测试任务
        for (int i = 0; i < routesToTest.size(); i++) {
            final RouteContent route = routesToTest.get(i);
            final int index = i;
            
            testExecutor.submit(() -> {
                if (!isTestRunning) return;

                try {
                    testApiEndpoint(route, selectedMethod, timeout);
                } catch (Exception e) {
                    // ✅ 这里不应该再有异常，因为testApiEndpoint已经处理了所有异常
                    callbacks.printError("意外异常: " + route.getFullUrl() + " - " + e.getMessage());
                    e.printStackTrace();
                } finally {
                    // ✅ 使用原子计数器来正确跟踪完成数量
                    int completed = completedTestCount.incrementAndGet();
                    
                    SwingUtilities.invokeLater(() -> {
                        progressBar.setValue(completed);
                        int progress = (int) (completed * 100.0 / totalTestCount);
                        progressBar.setString(progress + "%");
                        
                        // ✅ 基于实际完成数量判断是否结束测试
                        if (completed >= totalTestCount) {
                            callbacks.printOutput("[统计] 所有测试任务已完成，开始统计结果...");
                            finishTesting();
                        }
                    });
                }
            });
        }
    }
    
    /**
     * 停止API测试
     */
    private void stopApiTesting() {
        isTestRunning = false;
        if (testExecutor != null) {
            testExecutor.shutdownNow();
        }
        // ✅ 停止时不重置计数器，保持当前已完成的测试结果
        finishTesting();
        callbacks.printOutput("测试已停止");
    }
    
    /**
     * 完成测试
     */
    private void finishTesting() {
        isTestRunning = false;
        startTestButton.setEnabled(true);
        stopTestButton.setEnabled(false);
        
        SwingUtilities.invokeLater(() -> {
            // ✅ 添加调试信息，显示计数器状态
            int currentCompleted = completedTestCount.get();
            callbacks.printOutput("[调试] 调试信息: 完成计数=" + currentCompleted + ", 总测试数=" + totalTestCount + ", 结果集大小=" + testResults.size());
            
            statusLabel.setText("测试完成 - 共测试 " + testResults.size() + " 个端点");
            callbacks.printOutput("----------------------------------------");
            callbacks.printOutput("测试完成！总计: " + testResults.size() + " 个端点");
            
            // 统计结果
            long successCount = testResults.stream().filter(r -> r.isSuccess()).count();
            long errorCount = testResults.size() - successCount;
            callbacks.printOutput("成功: " + successCount + ", 失败: " + errorCount);
        });
    }
    
    /**
     * 测试单个API端点 - 使用Burp原生HTTP请求
     */
    private void testApiEndpoint(RouteContent route, String method, int timeout) {
        callbacks.printOutput("🚀 开始测试: " + route.getFullUrl() + " [" + method + "]");
        
        // ✅ 在方法开始就检查线程状态
        callbacks.printOutput("🔧 当前线程: " + Thread.currentThread().getName());
        callbacks.printOutput("🔧 是否为EDT线程: " + SwingUtilities.isEventDispatchThread());
        
        // ✅ 如果是EDT线程，给出警告但继续执行
        if (SwingUtilities.isEventDispatchThread()) {
            callbacks.printOutput("[警告] 警告: 在EDT线程中执行HTTP请求可能会导致问题");
        }

        long startTime = System.currentTimeMillis();
        ApiTestResult result = new ApiTestResult();
        result.setUrl(route.getFullUrl());
        result.setMethod(method);
        result.setTestTime(LocalDateTime.now());

        try {
            // ✅ 使用Burp原生HTTP请求功能
            callbacks.printOutput("📝 解析URL: " + route.getFullUrl());
            java.net.URL url = new java.net.URL(route.getFullUrl());

            // ✅ 创建HTTP服务对象
            callbacks.printOutput("🌐 创建HTTP服务: " + url.getHost() + ":" +
                (url.getPort() == -1 ? (url.getProtocol().equals("https") ? 443 : 80) : url.getPort()));
            IHttpService httpService = callbacks.getHelpers().buildHttpService(
                url.getHost(),
                url.getPort() == -1 ? (url.getProtocol().equals("https") ? 443 : 80) : url.getPort(),
                url.getProtocol()
            );

            // ✅ 构建HTTP请求
            callbacks.printOutput("[构建] 构建HTTP请求...");
            byte[] httpRequest = buildBurpHttpRequest(url, method);
            callbacks.printOutput("[构建] HTTP请求大小: " + httpRequest.length + " bytes");

            // ✅ 发送HTTP请求 - 使用Burp原生方法
            callbacks.printOutput("🚀 发送HTTP请求到: " + url.getHost());
            IHttpRequestResponse requestResponse = callbacks.makeHttpRequest(httpService, httpRequest);
            callbacks.printOutput("📨 收到HTTP响应");

            long responseTime = System.currentTimeMillis() - startTime;

            // ✅ 保存完整的请求响应对象
            result.setRequestResponse(requestResponse);

            // ✅ 解析响应
            callbacks.printOutput("[分析] 开始解析HTTP响应...");
            if (requestResponse.getResponse() != null) {
                callbacks.printOutput("[分析] 响应不为空，开始分析...");
                callbacks.printOutput("[分析] 响应字节长度: " + requestResponse.getResponse().length);

                // ✅ 优先尝试手动解析，避免Burp API兼容性问题
                boolean analysisSuccess = false;
                
                callbacks.printOutput("[解析] 开始解析响应 (优先手动解析)...");
                
                // 检查响应数据有效性
                if (requestResponse.getResponse() == null || requestResponse.getResponse().length == 0) {
                    callbacks.printOutput("❌ 响应数据为空");
                } else {
                    // 显示响应开始部分用于调试
                    String responseStart = new String(requestResponse.getResponse(), 0, 
                        Math.min(50, requestResponse.getResponse().length));
                    callbacks.printOutput("[解析] 响应开始: " + responseStart.replace("\r", "\\r").replace("\n", "\\n"));
                    
                    // ✅ 首先尝试手动解析
                    int manualStatusCode = parseHttpStatusCode(requestResponse.getResponse());
                    if (manualStatusCode != -1) {
                        callbacks.printOutput("[解析] 手动解析状态码成功: " + manualStatusCode);
                        
                        result.setStatusCode(manualStatusCode);
                        result.setResponseTime(responseTime);
                        result.setContentLength(requestResponse.getResponse().length);
                        result.setSuccess(manualStatusCode >= 200 && manualStatusCode < 400);
                        
                        callbacks.printOutput("[解析] 手动解析完成，设置基本信息完成");
                        analysisSuccess = true;
                    } else {
                        callbacks.printOutput("❌ 手动解析失败，尝试Burp原生方法...");
                        
                        // ✅ 手动解析失败，尝试Burp原生方法作为备用
                        try {
                            IResponseInfo responseInfo = callbacks.getHelpers().analyzeResponse(requestResponse.getResponse());
                            callbacks.printOutput("[解析] Burp原生分析成功，状态码: " + responseInfo.getStatusCode());
                            
                            result.setStatusCode(responseInfo.getStatusCode());
                            result.setResponseTime(responseTime);
                            result.setContentLength(requestResponse.getResponse().length - responseInfo.getBodyOffset());
                            result.setSuccess(responseInfo.getStatusCode() >= 200 && responseInfo.getStatusCode() < 400);
                            
                            analysisSuccess = true;
                        } catch (Throwable e) {
                            callbacks.printOutput("❌ Burp原生分析也失败: " + e.getClass().getName() + " - " + e.getMessage());
                            analysisSuccess = false;
                        }
                    }
                }

                // ✅ 如果解析失败，设置默认值
                if (!analysisSuccess) {
                    callbacks.printOutput("[解析] 所有解析方法都失败，使用默认值...");
                    result.setStatusCode(-1);
                    result.setResponseTime(responseTime);
                    result.setContentLength(requestResponse.getResponse().length);
                    result.setSuccess(false);
                    result.setErrorMessage("Response analysis failed");
                    callbacks.printOutput("[解析] 默认值设置完成");
                }

                // 更新路径的测试结果
                callbacks.printOutput("📝 更新路径测试结果...");
                try {
                    // ✅ 简单提取响应体（从固定偏移开始，避免解析头部）
                    String responseBody = "";
                    if (requestResponse.getResponse().length > 200) {
                        responseBody = new String(requestResponse.getResponse(), 200,
                            requestResponse.getResponse().length - 200);
                    }
                    callbacks.printOutput("📝 响应体长度: " + responseBody.length());

                    route.setTestResult(result.getStatusCode(), responseTime, "text/html", responseBody);
                    callbacks.printOutput("📝 路径测试结果更新完成");
                } catch (Exception e) {
                    callbacks.printError("❌ 响应体提取失败: " + e.getMessage());
                    route.setTestResult(result.getStatusCode(), responseTime, "unknown", "");
                }

            } else {
                // 没有响应
                result.setStatusCode(-1);
                result.setResponseTime(responseTime);
                result.setContentLength(0);
                result.setSuccess(false);
                result.setErrorMessage("No response received");
            }

        } catch (Throwable e) {  // ✅ 捕获所有类型的异常，包括Error
            callbacks.printOutput("❌ 外层捕获异常: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            result.setStatusCode(-1);
            result.setResponseTime(System.currentTimeMillis() - startTime);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            callbacks.printError("❌ HTTP请求异常: " + route.getFullUrl() + " - " + e.getMessage());
            e.printStackTrace();
            // ✅ 不重新抛出异常，让方法继续执行以添加错误结果
        }

        // ✅ 无论成功还是失败，都添加到结果列表并更新UI
        callbacks.printOutput("[添加] 添加测试结果到列表...");
        testResults.add(result);
        callbacks.printOutput("[添加] 测试结果已添加到列表，当前总数: " + testResults.size());

        // ✅ 创建ApiTestResultTab的结果对象，使用真实的HTTP数据
        callbacks.printOutput("[创建] 创建详细结果对象...");
        ApiTestResultTab.ApiTestResult detailedResult = new ApiTestResultTab.ApiTestResult(result.getUrl(), result.getMethod());
        detailedResult.setStatusCode(result.getStatusCode());
        detailedResult.setResponseTime(result.getResponseTime());
        detailedResult.setContentLength(result.getContentLength());
        detailedResult.setTestTime(result.getTestTime());
        detailedResult.setSuccess(result.isSuccess());
        detailedResult.setErrorMessage(result.getErrorMessage());

        // ✅ 设置真实的HTTP请求和响应数据
        if (result.getRequestResponse() != null) {
            // 使用真实的请求响应数据
            detailedResult.setRequestBytes(result.getRequestResponse().getRequest());
            detailedResult.setResponseBytes(result.getRequestResponse().getResponse());

            // 解析请求头和响应头
            if (result.getRequestResponse().getRequest() != null) {
                callbacks.printOutput("[处理] 处理请求数据，避免analyzeRequest...");
                
                try {
                    // 手动提取请求头和请求体
                    String requestStr = new String(result.getRequestResponse().getRequest());
                    String[] lines = requestStr.split("\\r?\\n");
                    
                    // 提取请求头
                    StringBuilder headers = new StringBuilder();
                    int bodyStartIndex = 0;
                    for (int i = 0; i < lines.length; i++) {
                        if (lines[i].trim().isEmpty()) {
                            bodyStartIndex = i + 1;
                            break;
                        }
                        headers.append(lines[i]).append("\n");
                    }
                    
                    detailedResult.setRequestHeaders(headers.toString());
                    
                    // 提取请求体（简单方式）
                    if (bodyStartIndex < lines.length) {
                        StringBuilder body = new StringBuilder();
                        for (int i = bodyStartIndex; i < lines.length; i++) {
                            body.append(lines[i]).append("\n");
                        }
                        detailedResult.setRequestBody(body.toString());
                    } else {
                        detailedResult.setRequestBody("");
                    }
                    
                    callbacks.printOutput("✅ 请求数据处理完成");
                } catch (Exception e) {
                    callbacks.printOutput("❌ 请求数据处理异常: " + e.getMessage());
                    detailedResult.setRequestHeaders("HTTP请求解析失败");
                    detailedResult.setRequestBody("请求体解析失败: " + e.getMessage());
                }
            }

            if (result.getRequestResponse().getResponse() != null) {
                // ✅ 避免再次调用有问题的analyzeResponse，直接处理响应
                callbacks.printOutput("[处理] 处理响应数据，避免analyzeResponse...");
                
                try {
                    // 手动提取响应头和响应体
                    String responseStr = new String(result.getRequestResponse().getResponse());
                    String[] lines = responseStr.split("\\r?\\n");
                    
                    // 提取响应头
                    StringBuilder headers = new StringBuilder();
                    int bodyStartIndex = 0;
                    for (int i = 0; i < lines.length; i++) {
                        if (lines[i].trim().isEmpty()) {
                            bodyStartIndex = i + 1;
                            break;
                        }
                        headers.append(lines[i]).append("\n");
                    }
                    
                    detailedResult.setResponseHeaders(headers.toString());
                    detailedResult.setStatusMessage(getStatusMessage(result.getStatusCode()));
                    
                    // 提取响应体（简单方式）
                    if (bodyStartIndex < lines.length) {
                        StringBuilder body = new StringBuilder();
                        for (int i = bodyStartIndex; i < lines.length; i++) {
                            body.append(lines[i]).append("\n");
                        }
                        detailedResult.setResponseBody(body.toString());
                    } else {
                        detailedResult.setResponseBody("");
                    }
                    
                    callbacks.printOutput("✅ 响应数据处理完成");
                } catch (Exception e) {
                    callbacks.printOutput("❌ 响应数据处理异常: " + e.getMessage());
                    detailedResult.setResponseHeaders("HTTP响应解析失败");
                    detailedResult.setResponseBody("响应体解析失败: " + e.getMessage());
                    detailedResult.setStatusMessage(getStatusMessage(result.getStatusCode()));
                }
            }
        } else {
            // 回退到构建的数据（用于错误情况）
            detailedResult.setRequestHeaders(buildRequestHeaders(result.getUrl(), result.getMethod()));
            detailedResult.setRequestBody(buildRequestBody(result.getMethod()));
            detailedResult.setResponseHeaders(buildResponseHeaders(result.getStatusCode(), result.getContentLength()));
            detailedResult.setResponseBody(buildMockResponseBody(result.getStatusCode()));
            detailedResult.setStatusMessage(getStatusMessage(result.getStatusCode()));

            // 构建完整的请求字节数据
            String requestString = buildFullHttpRequest(result.getUrl(), result.getMethod());
            detailedResult.setRequestBytes(requestString.getBytes());
            detailedResult.setResponseBytes(new byte[0]);
        }

        // ✅ 直接添加到集成的JsRouteScan风格测试结果Tab
        callbacks.printOutput("🎯 准备添加结果到UI...");
        try {
            callbacks.printOutput("🎯 调用testResultTab.addApiTestResult...");
            testResultTab.addApiTestResult(detailedResult);
            callbacks.printOutput("✅ 测试结果已添加: " + result.getUrl() + " - " + result.getStatusCode());
        } catch (Exception e) {
            callbacks.printError("❌ 添加测试结果失败: " + e.getMessage());
            e.printStackTrace();
        }

        callbacks.printOutput("🏁 testApiEndpoint方法即将结束");
        SwingUtilities.invokeLater(() -> {
            // 更新状态信息，不再需要日志
            callbacks.printOutput("测试完成: " + result.getUrl() + " - " + result.getStatusCode());
        });
        callbacks.printOutput("🏁 testApiEndpoint方法结束");
    }

    /**
     * 构建Burp原生HTTP请求
     */
    private byte[] buildBurpHttpRequest(java.net.URL url, String method) {
        StringBuilder request = new StringBuilder();

        // ✅ 请求行
        String path = url.getPath();
        if (path.isEmpty()) path = "/";
        if (url.getQuery() != null) {
            path += "?" + url.getQuery();
        }
        request.append(method).append(" ").append(path).append(" HTTP/1.1\r\n");

        // ✅ Host头
        request.append("Host: ").append(url.getHost());
        if (url.getPort() != -1 &&
            !((url.getProtocol().equals("http") && url.getPort() == 80) ||
              (url.getProtocol().equals("https") && url.getPort() == 443))) {
            request.append(":").append(url.getPort());
        }
        request.append("\r\n");

        // ✅ 添加自定义头部
        String customHeaders = customHeadersArea.getText().trim();
        if (!customHeaders.isEmpty()) {
            String[] headers = customHeaders.split("\n");
            for (String header : headers) {
                header = header.trim();
                if (!header.isEmpty() && header.contains(":")) {
                    request.append(header).append("\r\n");
                }
            }
        } else {
            // 默认头部
            request.append("User-Agent: Burp API Tester\r\n");
            request.append("Accept: application/json, */*\r\n");
        }

        // ✅ Connection头
        request.append("Connection: close\r\n");

        // ✅ 如果是POST/PUT请求，添加Content-Length
        if ("POST".equals(method) || "PUT".equals(method)) {
            request.append("Content-Type: application/json\r\n");
            request.append("Content-Length: 0\r\n");
        }

        // ✅ 结束头部
        request.append("\r\n");

        return request.toString().getBytes();
    }

    /**
     * 从响应头中提取Content-Type
     */
    private String getContentType(java.util.List<String> headers) {
        for (String header : headers) {
            if (header.toLowerCase().startsWith("content-type:")) {
                return header.substring(13).trim();
            }
        }
        return "text/html";
    }
    
    // ✅ 测试结果添加功能已集成到JsRouteScan风格的测试结果Tab中
    
    /**
     * 获取要测试的路径（智能处理基础路径）
     */
    private List<RouteContent> getRoutesToTest() {
        String selectedHost = (String) hostCombo.getSelectedItem();
        List<RouteContent> allRoutes = mainTab.getRouteList();
        List<RouteContent> filteredRoutes;

        // 首先按主机过滤
        if ("全部主机".equals(selectedHost)) {
            filteredRoutes = new ArrayList<>(allRoutes);
        } else {
            filteredRoutes = allRoutes.stream()
                .filter(route -> route.getHost().equals(selectedHost))
                .collect(java.util.stream.Collectors.toList());
        }

        // 如果设置了基础路径，则创建新的测试路径
        List<RouteContent> finalRoutes;
        if (customBasePath != null && !customBasePath.trim().isEmpty()) {
            finalRoutes = createRoutesWithBasePath(filteredRoutes);
        } else {
            // 没有设置基础路径，使用原始路径
            finalRoutes = filteredRoutes;
        }

        // ✅ 去重：根据完整URL去重
        return finalRoutes.stream()
            .collect(java.util.stream.Collectors.toMap(
                RouteContent::getFullUrl,  // 使用完整URL作为key
                route -> route,            // 值就是route本身
                (existing, replacement) -> existing  // 如果有重复，保留第一个
            ))
            .values()
            .stream()
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据基础路径创建测试路径
     */
    private List<RouteContent> createRoutesWithBasePath(List<RouteContent> originalRoutes) {
        List<RouteContent> testRoutes = new ArrayList<>();

        for (RouteContent originalRoute : originalRoutes) {
            String testUrl;
            String testHost;
            String testPath;

            if (customBasePath.startsWith("http://") || customBasePath.startsWith("https://")) {
                // 情况2: 完整URL基础路径 (如: http://baidu.com/qqq)
                testUrl = combineBasePath(customBasePath, originalRoute.getPath());
                try {
                    java.net.URL url = new java.net.URL(testUrl);
                    testHost = url.getHost();
                    if (url.getPort() != -1 && url.getPort() != 80 && url.getPort() != 443) {
                        testHost += ":" + url.getPort();
                    }
                    testPath = url.getPath();
                } catch (Exception e) {
                    // 解析失败，跳过这个路径
                    callbacks.printError("URL解析失败: " + testUrl);
                    continue;
                }
            } else {
                // 情况1: 相对路径基础路径 (如: /qqq)
                // 使用原始路径的来源域名/IP + 基础路径 + 发现的API
                String sourceUrl = originalRoute.getSourceFile();
                String sourceHost = extractHostFromSource(sourceUrl);

                if (sourceHost == null) {
                    // 如果无法从来源提取主机，使用选择的主机
                    sourceHost = originalRoute.getHost();
                }

                testHost = sourceHost;
                testPath = combineBasePath(customBasePath, originalRoute.getPath());
            }

            // 创建新的测试路径
            RouteContent testRoute = new RouteContent(testHost, testPath, "基础路径组合", "自定义基础路径测试");
            testRoute.addMethod("GET"); // 默认方法，会在测试时使用选择的方法
            testRoutes.add(testRoute);
        }

        return testRoutes;
    }

    /**
     * 从来源URL中提取主机信息
     */
    private String extractHostFromSource(String sourceUrl) {
        if (sourceUrl == null || sourceUrl.trim().isEmpty()) {
            return null;
        }

        try {
            java.net.URL url = new java.net.URL(sourceUrl);
            String host = url.getHost();
            int port = url.getPort();

            // 添加端口信息（如果不是默认端口）
            if (port != -1 && port != 80 && port != 443) {
                host += ":" + port;
            }

            return host;
        } catch (Exception e) {
            // 解析失败，返回null
            return null;
        }
    }
    
    /**
     * 刷新主机列表
     */
    private void refreshHostList() {
        SwingUtilities.invokeLater(() -> {
            try {
                String currentSelection = (String) hostCombo.getSelectedItem();

                // 临时移除监听器，避免在更新过程中触发事件
                ActionListener[] listeners = hostCombo.getActionListeners();
                for (ActionListener listener : listeners) {
                    hostCombo.removeActionListener(listener);
                }

                // 清空并重新添加选项
                hostCombo.removeAllItems();
                hostCombo.addItem("全部主机");

                // 添加所有主机
                mainTab.getHostMap().keySet().stream()
                    .sorted()
                    .forEach(hostCombo::addItem);

                // 尝试恢复之前的选择
                if (currentSelection != null) {
                    hostCombo.setSelectedItem(currentSelection);
                }

                // 重新添加监听器
                for (ActionListener listener : listeners) {
                    hostCombo.addActionListener(listener);
                }

            } catch (Exception e) {
                callbacks.printError("刷新主机列表时发生错误: " + e.getMessage());
            }
        });
    }
    
    // ✅ 所有表格操作和日志功能已集成到JsRouteScan风格的测试结果Tab中
    
    /**
     * 刷新数据
     */
    public void refreshData() {
        refreshHostList();
    }
    
    /**
     * 清除数据
     */
    public void clearData() {
        // ✅ 清除功能已集成到JsRouteScan风格的测试结果Tab中
        testResults.clear();
        callbacks.printOutput("已清除API测试数据");
    }
    
    /**
     * 设置自定义基础路径
     */
    private void setCustomBasePath() {
        String inputPath = customApiPathField.getText().trim();
        if (inputPath.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请输入基础路径", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 验证和标准化基础路径
        String basePath;
        if (inputPath.startsWith("http://") || inputPath.startsWith("https://")) {
            // 完整URL格式
            try {
                java.net.URL url = new java.net.URL(inputPath);
                // 确保URL以/结尾
                basePath = inputPath.endsWith("/") ? inputPath : inputPath + "/";
            } catch (Exception e) {
                JOptionPane.showMessageDialog(mainPanel, "无效的URL格式", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
        } else {
            // 相对路径格式
            if (!inputPath.startsWith("/")) {
                inputPath = "/" + inputPath;
            }
            if (!inputPath.endsWith("/")) {
                inputPath = inputPath + "/";
            }
            basePath = inputPath;
        }

        // 设置基础路径
        this.customBasePath = basePath;

        // 更新显示
        currentBasePathLabel.setText("当前基础路径: " + basePath);
        currentBasePathLabel.setForeground(Color.BLUE);

        // 清空输入框
        customApiPathField.setText("");

        callbacks.printOutput("已设置基础路径: " + basePath);
        callbacks.printOutput("提示: 测试时将使用 基础路径 + 发现的API路径 的组合");
    }

    // testWithCustomBasePath方法已移除，功能合并到startApiTesting中

    /**
     * 组合基础路径和API路径
     */
    private String combineBasePath(String basePath, String apiPath) {
        // 确保basePath以/结尾
        if (!basePath.endsWith("/")) {
            basePath = basePath + "/";
        }

        // 确保apiPath不以/开头（避免双斜杠）
        if (apiPath.startsWith("/")) {
            apiPath = apiPath.substring(1);
        }

        return basePath + apiPath;
    }

    /**
     * 获取面板
     */
    public JPanel getPanel() {
        return mainPanel;
    }
    
    /**
     * API测试结果类
     */
    private static class ApiTestResult {
        private String url;
        private String method;
        private int statusCode;
        private long responseTime;
        private int contentLength;
        private LocalDateTime testTime;
        private boolean success;
        private String errorMessage;
        private IHttpRequestResponse requestResponse; // ✅ 添加HTTP请求响应对象
        
        // Getter和Setter方法
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        
        public int getStatusCode() { return statusCode; }
        public void setStatusCode(int statusCode) { this.statusCode = statusCode; }
        
        public long getResponseTime() { return responseTime; }
        public void setResponseTime(long responseTime) { this.responseTime = responseTime; }
        
        public int getContentLength() { return contentLength; }
        public void setContentLength(int contentLength) { this.contentLength = contentLength; }
        
        public LocalDateTime getTestTime() { return testTime; }
        public void setTestTime(LocalDateTime testTime) { this.testTime = testTime; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public IHttpRequestResponse getRequestResponse() { return requestResponse; }
        public void setRequestResponse(IHttpRequestResponse requestResponse) { this.requestResponse = requestResponse; }
    }

    /**
     * 构建请求头
     */
    private String buildRequestHeaders(String url, String method) {
        StringBuilder headers = new StringBuilder();

        try {
            java.net.URL urlObj = new java.net.URL(url);
            headers.append("Host: ").append(urlObj.getHost());
            if (urlObj.getPort() != -1 && urlObj.getPort() != 80 && urlObj.getPort() != 443) {
                headers.append(":").append(urlObj.getPort());
            }
            headers.append("\n");
        } catch (Exception e) {
            headers.append("Host: unknown\n");
        }

        headers.append("User-Agent: Burp API Checker\n");
        headers.append("Accept: application/json, */*\n");
        headers.append("Connection: close\n");

        // 根据方法添加特定头
        if ("POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method)) {
            headers.append("Content-Type: application/json\n");
        }

        return headers.toString();
    }

    /**
     * 构建请求体
     */
    private String buildRequestBody(String method) {
        if ("POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method)) {
            return "{}"; // 简单的JSON请求体
        }
        return null;
    }

    /**
     * 构建响应头
     */
    private String buildResponseHeaders(int statusCode, int contentLength) {
        StringBuilder headers = new StringBuilder();

        headers.append("Content-Type: application/json\n");
        headers.append("Content-Length: ").append(contentLength).append("\n");
        headers.append("Server: Mock Server\n");
        headers.append("Date: ").append(new java.util.Date().toString()).append("\n");

        if (statusCode >= 300 && statusCode < 400) {
            headers.append("Location: /redirected\n");
        }

        return headers.toString();
    }

    /**
     * 构建模拟响应体
     */
    private String buildMockResponseBody(int statusCode) {
        switch (statusCode) {
            case 200:
                return "{\"status\":\"success\",\"message\":\"Request processed successfully\",\"data\":{\"id\":1,\"name\":\"test\"}}";
            case 201:
                return "{\"status\":\"created\",\"message\":\"Resource created successfully\",\"id\":123}";
            case 400:
                return "{\"error\":\"Bad Request\",\"message\":\"Invalid request parameters\"}";
            case 401:
                return "{\"error\":\"Unauthorized\",\"message\":\"Authentication required\"}";
            case 403:
                return "{\"error\":\"Forbidden\",\"message\":\"Access denied\"}";
            case 404:
                return "{\"error\":\"Not Found\",\"message\":\"Resource not found\"}";
            case 500:
                return "{\"error\":\"Internal Server Error\",\"message\":\"An unexpected error occurred\"}";
            default:
                return "{\"status\":\"unknown\",\"code\":" + statusCode + "}";
        }
    }

    /**
     * 获取状态消息
     */
    private String getStatusMessage(int statusCode) {
        switch (statusCode) {
            case 200: return "OK";
            case 201: return "Created";
            case 204: return "No Content";
            case 301: return "Moved Permanently";
            case 302: return "Found";
            case 400: return "Bad Request";
            case 401: return "Unauthorized";
            case 403: return "Forbidden";
            case 404: return "Not Found";
            case 405: return "Method Not Allowed";
            case 500: return "Internal Server Error";
            case 502: return "Bad Gateway";
            case 503: return "Service Unavailable";
            default: return "Unknown";
        }
    }

    /**
     * 构建完整的HTTP请求
     */
    private String buildFullHttpRequest(String url, String method) {
        StringBuilder request = new StringBuilder();

        try {
            java.net.URL urlObj = new java.net.URL(url);
            String path = urlObj.getPath();
            if (urlObj.getQuery() != null) {
                path += "?" + urlObj.getQuery();
            }

            // 请求行
            request.append(method).append(" ").append(path).append(" HTTP/1.1\r\n");

            // 请求头
            request.append(buildRequestHeaders(url, method).replace("\n", "\r\n"));

            // 请求体
            String body = buildRequestBody(method);
            if (body != null && !body.isEmpty()) {
                request.append("Content-Length: ").append(body.getBytes().length).append("\r\n");
                request.append("\r\n");
                request.append(body);
            } else {
                request.append("\r\n");
            }

        } catch (Exception e) {
            // 如果URL解析失败，构建简单请求
            request.append(method).append(" ").append(url).append(" HTTP/1.1\r\n");
            request.append("User-Agent: Burp API Checker\r\n");
            request.append("Accept: */*\r\n");
            request.append("Connection: close\r\n");
            request.append("\r\n");
        }

        return request.toString();
    }
}
