package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.ActionListener;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 敏感信息Tab - 集成现有的敏感信息提取功能
 * 提供敏感信息的分类展示和管理
 */
public class SensitiveInfoTab {
    
    private IBurpExtenderCallbacks callbacks;
    private ApiScanMainTab mainTab;
    
    // UI组件
    private JPanel mainPanel;
    private JSplitPane splitPane;
    private JTable sensitiveTable;
    private DefaultTableModel tableModel;
    private TableRowSorter<DefaultTableModel> sorter;
    private JTextArea detailArea;
    private JLabel statusLabel;
    private JComboBox<String> hostFilterCombo;
    private JComboBox<String> typeFilterCombo;
    private JComboBox<String> riskFilterCombo;
    
    // 数据
    private List<EnhancedSensitiveInfoExtractor.SensitiveDataItem> sensitiveDataList;
    
    // 表格列定义
    private static final String[] COLUMN_NAMES = {
        "选择", "类型", "敏感信息", "主机", "来源", "风险等级", "发现时间", "重要性"
    };
    
    public SensitiveInfoTab(IBurpExtenderCallbacks callbacks, ApiScanMainTab mainTab) {
        this.callbacks = callbacks;
        this.mainTab = mainTab;
        this.sensitiveDataList = new ArrayList<>();
        
        initializeUI();
    }
    
    /**
     * 初始化用户界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建顶部工具栏
        JPanel toolbar = createToolbar();
        mainPanel.add(toolbar, BorderLayout.NORTH);
        
        // 创建主要内容区域
        splitPane = createMainContent();
        mainPanel.add(splitPane, BorderLayout.CENTER);
        
        // 创建底部状态栏
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建工具栏
     */
    private JPanel createToolbar() {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        toolbar.setBorder(BorderFactory.createEtchedBorder());
        
        // 主机过滤
        toolbar.add(new JLabel("主机:"));
        hostFilterCombo = new JComboBox<>();
        hostFilterCombo.addItem("全部主机");
        hostFilterCombo.addActionListener(e -> filterSensitiveData());
        toolbar.add(hostFilterCombo);
        
        // 类型过滤器 - 使用统一配置动态生成选项
        toolbar.add(new JLabel("类型:"));
        typeFilterCombo = new JComboBox<>();
        typeFilterCombo.addItem("全部类型");
        
        // 按分类添加所有敏感信息类型
        Map<String, List<String>> categories = SensitiveInfoConfig.getCategories();
        for (Map.Entry<String, List<String>> entry : categories.entrySet()) {
            String categoryName = entry.getKey();
            // 添加分类标题（可选）
            // typeFilterCombo.addItem("=== " + categoryName + " ===");
            
            // 添加该分类下的所有类型
            for (String typeName : entry.getValue()) {
                SensitiveInfoConfig.SensitivePattern pattern = SensitiveInfoConfig.getPatternByName(typeName);
                if (pattern != null) {
                    String displayName = typeName + " (" + SensitiveInfoConfig.getRiskDescription(pattern.riskLevel) + ")";
                    typeFilterCombo.addItem(typeName); // 使用原始名称作为值，便于筛选
                }
            }
        }
        
        typeFilterCombo.addActionListener(e -> filterSensitiveData());
        toolbar.add(typeFilterCombo);
        
        // 风险等级过滤
        toolbar.add(new JLabel("风险:"));
        riskFilterCombo = new JComboBox<>();
        riskFilterCombo.addItem("全部风险");
        riskFilterCombo.addItem("高风险");
        riskFilterCombo.addItem("中风险");
        riskFilterCombo.addItem("低风险");
        riskFilterCombo.addActionListener(e -> filterSensitiveData());
        toolbar.add(riskFilterCombo);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 全选按钮
        JButton selectAllButton = new JButton("全选");
        selectAllButton.addActionListener(e -> selectAllItems(true));
        toolbar.add(selectAllButton);
        
        // 取消全选按钮
        JButton deselectAllButton = new JButton("取消全选");
        deselectAllButton.addActionListener(e -> selectAllItems(false));
        toolbar.add(deselectAllButton);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 复制选中按钮
        JButton copySelectedButton = new JButton("复制选中");
        copySelectedButton.addActionListener(e -> copySelectedItems());
        toolbar.add(copySelectedButton);
        
        // 标记重要按钮
        JButton markImportantButton = new JButton("标记重要");
        markImportantButton.addActionListener(e -> markSelectedAsImportant());
        toolbar.add(markImportantButton);
        
        // 刷新按钮
        JButton refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> refreshData());
        toolbar.add(refreshButton);
        
        // 清除按钮
        JButton clearButton = new JButton("清除");
        clearButton.addActionListener(e -> clearSelectedItems());
        toolbar.add(clearButton);
        
        // 导出按钮
        JButton exportButton = new JButton("导出");
        exportButton.addActionListener(e -> exportSensitiveData());
        toolbar.add(exportButton);

        // 搜索框
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        toolbar.add(new JLabel("搜索:"));
        JTextField searchField = new JTextField(15);
        searchField.setToolTipText("搜索敏感信息...");
        searchField.addActionListener(e -> searchSensitiveData(searchField.getText()));
        toolbar.add(searchField);

        JButton searchButton = new JButton("搜索");
        searchButton.addActionListener(e -> searchSensitiveData(searchField.getText()));
        toolbar.add(searchButton);
        
        return toolbar;
    }
    
    /**
     * 创建主要内容区域
     */
    private JSplitPane createMainContent() {
        // 创建表格
        tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 0) {
                    return Boolean.class; // 选择列
                }
                return String.class;
            }
            
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0; // 只有选择列可编辑
            }
        };
        
        sensitiveTable = new JTable(tableModel);
        sensitiveTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        sensitiveTable.setAutoCreateRowSorter(true);
        
        // 设置列宽
        sensitiveTable.getColumnModel().getColumn(0).setMaxWidth(50);  // 选择列
        sensitiveTable.getColumnModel().getColumn(1).setMaxWidth(120); // 类型列
        sensitiveTable.getColumnModel().getColumn(3).setMaxWidth(150); // 主机列
        sensitiveTable.getColumnModel().getColumn(5).setMaxWidth(80);  // 风险等级列
        sensitiveTable.getColumnModel().getColumn(6).setMaxWidth(120); // 发现时间列
        sensitiveTable.getColumnModel().getColumn(7).setMaxWidth(80);  // 重要性列
        
        // 添加表格选择监听器
        sensitiveTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showSensitiveDetails();
            }
        });
        
        // 添加右键菜单
        addContextMenu();
        
        JScrollPane tableScrollPane = new JScrollPane(sensitiveTable);
        tableScrollPane.setPreferredSize(new Dimension(800, 400));
        
        // 创建详情面板
        detailArea = new JTextArea();
        detailArea.setEditable(false);
        detailArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailArea.setText("选择敏感信息以查看详细信息...");
        
        JScrollPane detailScrollPane = new JScrollPane(detailArea);
        detailScrollPane.setPreferredSize(new Dimension(800, 200));
        detailScrollPane.setBorder(BorderFactory.createTitledBorder("敏感信息详情"));
        
        // 创建分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, tableScrollPane, detailScrollPane);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.7);
        
        return splitPane;
    }
    
    /**
     * 创建状态栏
     */
    private JPanel createStatusPanel() {
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        statusLabel = new JLabel("敏感信息总数: 0 | 选中: 0 | 高风险: 0 | 重要: 0");
        statusPanel.add(statusLabel);
        
        return statusPanel;
    }
    
    /**
     * 添加右键菜单
     */
    private void addContextMenu() {
        JPopupMenu contextMenu = new JPopupMenu();
        
        JMenuItem viewDetailsItem = new JMenuItem("查看详情");
        viewDetailsItem.addActionListener(e -> showSensitiveDetails());
        contextMenu.add(viewDetailsItem);
        
        JMenuItem copyValueItem = new JMenuItem("复制敏感信息");
        copyValueItem.addActionListener(e -> copySelectedValue());
        contextMenu.add(copyValueItem);
        
        JMenuItem copyUrlItem = new JMenuItem("复制来源URL");
        copyUrlItem.addActionListener(e -> copySelectedUrl());
        contextMenu.add(copyUrlItem);
        
        contextMenu.addSeparator();
        
        JMenuItem markImportantItem = new JMenuItem("标记为重要");
        markImportantItem.addActionListener(e -> markSelectedAsImportant());
        contextMenu.add(markImportantItem);
        
        JMenuItem unmarkImportantItem = new JMenuItem("取消重要标记");
        unmarkImportantItem.addActionListener(e -> unmarkSelectedAsImportant());
        contextMenu.add(unmarkImportantItem);
        
        contextMenu.addSeparator();
        
        JMenuItem removeItem = new JMenuItem("移除");
        removeItem.addActionListener(e -> removeSelectedItem());
        contextMenu.add(removeItem);
        
        sensitiveTable.setComponentPopupMenu(contextMenu);
    }
    
    /**
     * 添加敏感信息（改进的去重逻辑）
     */
    public void addSensitiveData(EnhancedSensitiveInfoExtractor.SensitiveDataItem item) {
        // 改进的去重逻辑：检查类型、值和来源是否完全相同
        boolean isDuplicate = sensitiveDataList.stream().anyMatch(existing ->
            existing.type.equals(item.type) &&
            existing.value.equals(item.value) &&
            existing.source.equals(item.source)
        );

        if (!isDuplicate) {
            sensitiveDataList.add(item);
            updateTableRow(item);
            updateHostFilter();
            updateStatusLabel();
        } else {
            // 可选：输出去重信息用于调试
            // callbacks.printOutput("跳过重复敏感信息: " + item.type + " - " + item.value + " (来源: " + item.source + ")");
        }
    }
    
    /**
     * 更新表格行
     */
    private void updateTableRow(EnhancedSensitiveInfoExtractor.SensitiveDataItem item) {
        SwingUtilities.invokeLater(() -> {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm");
            String foundTime = item.foundTime != null ? item.foundTime.format(formatter) : "未知";
            
            // 从URL中提取主机
            String host = extractHost(item.url);
            
            // 确定风险等级
            String riskLevel = getRiskLevel(item.type);
            
            // 截断敏感信息显示
            String displayValue = item.value;
            if (displayValue.length() > 50) {
                displayValue = displayValue.substring(0, 47) + "...";
            }
            
            Object[] rowData = {
                false, // 选择状态
                item.type,
                displayValue,
                host,
                item.source,
                riskLevel,
                foundTime,
                item.isImportant ? "★" : ""
            };
            
            tableModel.addRow(rowData);
        });
    }
    
    /**
     * 从URL中提取主机信息 - 专门针对Burp Suite的HTTP/HTTPS流量
     */
    private String extractHost(String url) {
        try {
            if (url == null || url.trim().isEmpty()) {
                return "未知主机";
            }

            String trimmedUrl = url.trim();

            // Burp只处理HTTP/HTTPS流量，所以URL应该总是以http://或https://开头
            if (trimmedUrl.startsWith("http://") || trimmedUrl.startsWith("https://")) {
                java.net.URL urlObj = new java.net.URL(trimmedUrl);
                String host = urlObj.getHost();

                if (host == null || host.trim().isEmpty()) {
                    return "未知主机";
                }

                int port = urlObj.getPort();

                // 如果端口不是默认端口，则包含端口号
                if (port != -1 && port != 80 && port != 443) {
                    return host + ":" + port;
                }
                return host;
            } else {
                // 这种情况不应该发生，因为Burp只处理HTTP/HTTPS
                return "非HTTP协议";
            }

        } catch (Exception e) {
            return "解析失败";
        }
    }
    
    /**
     * 获取风险等级
     */
    private String getRiskLevel(String type) {
        if (type.contains("JWT Token") || type.contains("API Key") || 
            type.contains("密码") || type.contains("数据库连接") || 
            type.contains("私钥")) {
            return "高风险";
        } else if (type.contains("身份证") || type.contains("手机号") || 
                  type.contains("邮箱")) {
            return "中风险";
        } else {
            return "低风险";
        }
    }
    
    /**
     * 更新主机过滤器
     */
    private void updateHostFilter() {
        SwingUtilities.invokeLater(() -> {
            try {
                String currentSelection = (String) hostFilterCombo.getSelectedItem();

                // 临时移除监听器，避免在更新过程中触发事件
                ActionListener[] listeners = hostFilterCombo.getActionListeners();
                for (ActionListener listener : listeners) {
                    hostFilterCombo.removeActionListener(listener);
                }

                // 清空并重新添加选项
                hostFilterCombo.removeAllItems();
                hostFilterCombo.addItem("全部主机");

                // 添加所有主机
                sensitiveDataList.stream()
                    .map(item -> extractHost(item.url))
                    .distinct()
                    .sorted()
                    .forEach(hostFilterCombo::addItem);

                // 尝试恢复之前的选择
                if (currentSelection != null) {
                    hostFilterCombo.setSelectedItem(currentSelection);
                }

                // 重新添加监听器
                for (ActionListener listener : listeners) {
                    hostFilterCombo.addActionListener(listener);
                }

            } catch (Exception e) {
                callbacks.printError("更新主机过滤器时发生错误: " + e.getMessage());
            }
        });
    }
    
    /**
     * 过滤敏感信息
     */
    private void filterSensitiveData() {
        String selectedHost = (String) hostFilterCombo.getSelectedItem();
        String selectedType = (String) typeFilterCombo.getSelectedItem();
        String selectedRisk = (String) riskFilterCombo.getSelectedItem();
        
        if ("全部主机".equals(selectedHost) && "全部类型".equals(selectedType) && "全部风险".equals(selectedRisk)) {
            sensitiveTable.setRowSorter(null);
        } else {
            RowFilter<DefaultTableModel, Object> filter = new RowFilter<DefaultTableModel, Object>() {
                @Override
                public boolean include(Entry<? extends DefaultTableModel, ? extends Object> entry) {
                    boolean hostMatch = "全部主机".equals(selectedHost) || 
                        selectedHost.equals(entry.getStringValue(3));
                    boolean typeMatch = "全部类型".equals(selectedType) || 
                        selectedType.equals(entry.getStringValue(1));
                    boolean riskMatch = "全部风险".equals(selectedRisk) || 
                        selectedRisk.equals(entry.getStringValue(5));
                    return hostMatch && typeMatch && riskMatch;
                }
            };
            
            sorter = new TableRowSorter<>(tableModel);
            sorter.setRowFilter(filter);
            sensitiveTable.setRowSorter(sorter);
        }
        
        updateStatusLabel();
    }
    
    /**
     * 显示敏感信息详情
     */
    private void showSensitiveDetails() {
        int selectedRow = sensitiveTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = sensitiveTable.convertRowIndexToModel(selectedRow);
            String type = (String) tableModel.getValueAt(modelRow, 1);
            String value = (String) tableModel.getValueAt(modelRow, 2);
            
            EnhancedSensitiveInfoExtractor.SensitiveDataItem item = findSensitiveItem(type, value);
            if (item != null) {
                displaySensitiveDetails(item);
            }
        }
    }
    
    /**
     * 显示敏感信息详细信息
     */
    private void displaySensitiveDetails(EnhancedSensitiveInfoExtractor.SensitiveDataItem item) {
        StringBuilder details = new StringBuilder();
        details.append("敏感信息详情\n");
        for (int i = 0; i < 50; i++) details.append("=");
        details.append("\n\n");
        
        details.append("基本信息:\n");
        details.append("  类型: ").append(item.type).append("\n");
        details.append("  敏感信息: ").append(item.value).append("\n");
        details.append("  描述: ").append(item.description != null ? item.description : "无").append("\n");
        details.append("  风险等级: ").append(getRiskLevel(item.type)).append("\n");
        details.append("  重要性: ").append(item.isImportant ? "重要" : "普通").append("\n\n");
        
        details.append("来源信息:\n");
        details.append("  来源: ").append(item.source).append("\n");
        details.append("  URL: ").append(item.url).append("\n");
        details.append("  主机: ").append(extractHost(item.url)).append("\n");
        details.append("  发现时间: ").append(item.foundTime != null ? item.foundTime : "未知").append("\n\n");
        
        details.append("安全建议:\n");
        details.append(getSecurityAdvice(item.type));
        
        detailArea.setText(details.toString());
        detailArea.setCaretPosition(0);
    }
    
    /**
     * 获取安全建议
     */
    private String getSecurityAdvice(String type) {
        switch (type) {
            case "JWT Token":
                return "  • JWT Token不应在客户端代码中硬编码\n" +
                       "  • 应使用安全的存储方式（如HttpOnly Cookie）\n" +
                       "  • 定期轮换Token以降低泄露风险\n";
            case "API Key":
                return "  • API Key应存储在环境变量或安全配置中\n" +
                       "  • 不应在前端代码中暴露\n" +
                       "  • 应设置适当的权限和访问限制\n";
            case "密码配置":
                return "  • 密码不应以明文形式存储\n" +
                       "  • 应使用强密码策略\n" +
                       "  • 考虑使用密码管理工具\n";
            case "数据库连接":
                return "  • 数据库连接字符串不应在客户端暴露\n" +
                       "  • 应使用最小权限原则\n" +
                       "  • 启用数据库访问日志和监控\n";
            case "身份证号":
                return "  • 个人身份信息需要严格保护\n" +
                       "  • 遵守相关隐私法规（如GDPR、个保法）\n" +
                       "  • 考虑数据脱敏处理\n";
            default:
                return "  • 评估此信息的敏感程度\n" +
                       "  • 考虑是否需要额外的保护措施\n" +
                       "  • 定期审查和更新安全策略\n";
        }
    }
    
    /**
     * 查找敏感信息项
     */
    private EnhancedSensitiveInfoExtractor.SensitiveDataItem findSensitiveItem(String type, String displayValue) {
        return sensitiveDataList.stream()
            .filter(item -> item.type.equals(type) && 
                (item.value.equals(displayValue) || item.value.startsWith(displayValue.replace("...", ""))))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 全选/取消全选
     */
    private void selectAllItems(boolean select) {
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            tableModel.setValueAt(select, i, 0);
        }
        updateStatusLabel();
    }
    
    /**
     * 复制选中的敏感信息
     */
    private void copySelectedItems() {
        List<String> selectedValues = new ArrayList<>();
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            Boolean selected = (Boolean) tableModel.getValueAt(i, 0);
            if (selected != null && selected) {
                String type = (String) tableModel.getValueAt(i, 1);
                String value = (String) tableModel.getValueAt(i, 2);
                selectedValues.add(type + ": " + value);
            }
        }
        
        if (!selectedValues.isEmpty()) {
            String result = String.join("\n", selectedValues);
            Toolkit.getDefaultToolkit().getSystemClipboard()
                .setContents(new java.awt.datatransfer.StringSelection(result), null);
            callbacks.printOutput("已复制 " + selectedValues.size() + " 项敏感信息");
        } else {
            JOptionPane.showMessageDialog(mainPanel, "请先选择要复制的敏感信息");
        }
    }
    
    /**
     * 复制选中的值
     */
    private void copySelectedValue() {
        int selectedRow = sensitiveTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = sensitiveTable.convertRowIndexToModel(selectedRow);
            String type = (String) tableModel.getValueAt(modelRow, 1);
            String displayValue = (String) tableModel.getValueAt(modelRow, 2);
            
            EnhancedSensitiveInfoExtractor.SensitiveDataItem item = findSensitiveItem(type, displayValue);
            if (item != null) {
                Toolkit.getDefaultToolkit().getSystemClipboard()
                    .setContents(new java.awt.datatransfer.StringSelection(item.value), null);
                callbacks.printOutput("已复制敏感信息: " + item.type);
            }
        }
    }
    
    /**
     * 复制选中的URL
     */
    private void copySelectedUrl() {
        int selectedRow = sensitiveTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = sensitiveTable.convertRowIndexToModel(selectedRow);
            String type = (String) tableModel.getValueAt(modelRow, 1);
            String displayValue = (String) tableModel.getValueAt(modelRow, 2);
            
            EnhancedSensitiveInfoExtractor.SensitiveDataItem item = findSensitiveItem(type, displayValue);
            if (item != null) {
                Toolkit.getDefaultToolkit().getSystemClipboard()
                    .setContents(new java.awt.datatransfer.StringSelection(item.url), null);
                callbacks.printOutput("已复制来源URL: " + item.url);
            }
        }
    }
    
    /**
     * 标记选中项为重要
     */
    private void markSelectedAsImportant() {
        List<Integer> selectedRows = getSelectedRows();
        if (selectedRows.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请先选择要标记的敏感信息");
            return;
        }
        
        for (int row : selectedRows) {
            String type = (String) tableModel.getValueAt(row, 1);
            String displayValue = (String) tableModel.getValueAt(row, 2);
            
            EnhancedSensitiveInfoExtractor.SensitiveDataItem item = findSensitiveItem(type, displayValue);
            if (item != null) {
                item.isImportant = true;
                tableModel.setValueAt("★", row, 7);
            }
        }
        
        updateStatusLabel();
        callbacks.printOutput("已标记 " + selectedRows.size() + " 项为重要");
    }
    
    /**
     * 取消重要标记
     */
    private void unmarkSelectedAsImportant() {
        int selectedRow = sensitiveTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = sensitiveTable.convertRowIndexToModel(selectedRow);
            String type = (String) tableModel.getValueAt(modelRow, 1);
            String displayValue = (String) tableModel.getValueAt(modelRow, 2);
            
            EnhancedSensitiveInfoExtractor.SensitiveDataItem item = findSensitiveItem(type, displayValue);
            if (item != null) {
                item.isImportant = false;
                tableModel.setValueAt("", modelRow, 7);
                updateStatusLabel();
                callbacks.printOutput("已取消重要标记: " + item.type);
            }
        }
    }
    
    /**
     * 移除选中项
     */
    private void removeSelectedItem() {
        int selectedRow = sensitiveTable.getSelectedRow();
        if (selectedRow >= 0) {
            int result = JOptionPane.showConfirmDialog(
                mainPanel,
                "确定要移除选中的敏感信息吗？",
                "确认移除",
                JOptionPane.YES_NO_OPTION
            );
            
            if (result == JOptionPane.YES_OPTION) {
                int modelRow = sensitiveTable.convertRowIndexToModel(selectedRow);
                String type = (String) tableModel.getValueAt(modelRow, 1);
                String displayValue = (String) tableModel.getValueAt(modelRow, 2);
                
                // 从数据中移除
                sensitiveDataList.removeIf(item -> item.type.equals(type) && 
                    (item.value.equals(displayValue) || item.value.startsWith(displayValue.replace("...", ""))));
                
                // 从表格中移除
                tableModel.removeRow(modelRow);
                
                updateStatusLabel();
                callbacks.printOutput("已移除敏感信息: " + type);
            }
        }
    }
    
    /**
     * 清除选中项
     */
    private void clearSelectedItems() {
        List<Integer> selectedRows = getSelectedRows();
        if (selectedRows.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请先选择要清除的敏感信息");
            return;
        }
        
        int result = JOptionPane.showConfirmDialog(
            mainPanel,
            "确定要清除 " + selectedRows.size() + " 项选中的敏感信息吗？",
            "确认清除",
            JOptionPane.YES_NO_OPTION
        );
        
        if (result == JOptionPane.YES_OPTION) {
            // 从后往前删除，避免索引变化
            for (int i = selectedRows.size() - 1; i >= 0; i--) {
                int row = selectedRows.get(i);
                String type = (String) tableModel.getValueAt(row, 1);
                String displayValue = (String) tableModel.getValueAt(row, 2);
                
                // 从数据中移除
                sensitiveDataList.removeIf(item -> item.type.equals(type) && 
                    (item.value.equals(displayValue) || item.value.startsWith(displayValue.replace("...", ""))));
                
                // 从表格中移除
                tableModel.removeRow(row);
            }
            
            updateStatusLabel();
            callbacks.printOutput("已清除 " + selectedRows.size() + " 项敏感信息");
        }
    }
    
    /**
     * 搜索敏感信息
     */
    private void searchSensitiveData(String searchText) {
        if (searchText.trim().isEmpty()) {
            sensitiveTable.setRowSorter(null);
        } else {
            RowFilter<DefaultTableModel, Object> filter = RowFilter.regexFilter("(?i)" + searchText);
            sorter = new TableRowSorter<>(tableModel);
            sorter.setRowFilter(filter);
            sensitiveTable.setRowSorter(sorter);
        }
        updateStatusLabel();
    }

    /**
     * 导出敏感信息数据
     */
    private void exportSensitiveData() {
        // TODO: 实现敏感信息数据导出功能
        callbacks.printOutput("导出敏感信息数据功能待实现");
    }
    
    /**
     * 获取选中的行
     */
    private List<Integer> getSelectedRows() {
        List<Integer> selectedRows = new ArrayList<>();
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            Boolean selected = (Boolean) tableModel.getValueAt(i, 0);
            if (selected != null && selected) {
                selectedRows.add(i);
            }
        }
        return selectedRows;
    }
    
    /**
     * 更新状态标签
     */
    private void updateStatusLabel() {
        SwingUtilities.invokeLater(() -> {
            int totalItems = sensitiveDataList.size();
            int highRiskItems = (int) sensitiveDataList.stream()
                .filter(item -> getRiskLevel(item.type).equals("高风险"))
                .count();
            int importantItems = (int) sensitiveDataList.stream()
                .filter(item -> item.isImportant)
                .count();
            
            int selectedCount = 0;
            for (int i = 0; i < tableModel.getRowCount(); i++) {
                Boolean selected = (Boolean) tableModel.getValueAt(i, 0);
                if (selected != null && selected) {
                    selectedCount++;
                }
            }
            
            statusLabel.setText(String.format("敏感信息总数: %d | 选中: %d | 高风险: %d | 重要: %d", 
                totalItems, selectedCount, highRiskItems, importantItems));
        });
    }
    
    /**
     * 刷新数据
     */
    public void refreshData() {
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            for (EnhancedSensitiveInfoExtractor.SensitiveDataItem item : sensitiveDataList) {
                updateTableRow(item);
            }
            updateHostFilter();
            updateStatusLabel();
        });
    }
    
    /**
     * 清除数据
     */
    public void clearData() {
        sensitiveDataList.clear();
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            detailArea.setText("选择敏感信息以查看详细信息...");
            updateHostFilter();
            updateStatusLabel();
        });
    }
    
    /**
     * 获取面板
     */
    public JPanel getPanel() {
        return mainPanel;
    }
}
