package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;

/**
 * 完全按照JsRouteScan风格实现的API测试结果Tab
 * 参考JsRouteScan项目的scan页面设计和功能
 */
public class JsRouteScanStyleTestResultTab {
    
    private IBurpExtenderCallbacks callbacks;
    private ApiScanMainTab mainTab;
    
    // 主要UI组件
    private JPanel mainPanel;
    private JSplitPane splitPane;
    private JTable resultTable;
    private DefaultTableModel tableModel;
    private JLabel statusLabel;
    
    // JsRouteScan风格的HTTP消息查看器
    private IMessageEditor requestViewer;
    private IMessageEditor responseViewer;
    private JTabbedPane messageTabPane;
    
    // 工具栏组件
    private JTextField searchField;
    private JComboBox<String> statusFilterCombo;
    private JComboBox<String> methodFilterCombo;
    private JButton clearButton;
    private JButton exportButton;
    
    // 数据存储
    private List<ScanResult> scanResults;
    private ScanResult currentSelectedResult;
    
    // 表格列定义 - 完全按照JsRouteScan的列结构
    private static final String[] COLUMN_NAMES = {
        "#", "URL", "Method", "Status", "Length", "Time", "Title"
    };
    
    public JsRouteScanStyleTestResultTab(IBurpExtenderCallbacks callbacks, ApiScanMainTab mainTab) {
        this.callbacks = callbacks;
        this.mainTab = mainTab;
        this.scanResults = new ArrayList<>();
        
        initializeUI();
    }
    
    /**
     * 初始化UI - 完全按照JsRouteScan的布局
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建工具栏
        JPanel toolbar = createToolbar();
        mainPanel.add(toolbar, BorderLayout.NORTH);
        
        // 创建分割面板 - 上下分割，与JsRouteScan一致
        splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        splitPane.setResizeWeight(0.6); // 上方60%，下方40%
        
        // 创建结果表格
        createResultTable();
        JScrollPane tableScrollPane = new JScrollPane(resultTable);
        splitPane.setTopComponent(tableScrollPane);
        
        // 创建HTTP消息查看器
        createMessageViewer();
        splitPane.setBottomComponent(messageTabPane);
        
        mainPanel.add(splitPane, BorderLayout.CENTER);
        
        // 创建状态栏
        createStatusBar();
        mainPanel.add(statusLabel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建工具栏 - JsRouteScan风格
     */
    private JPanel createToolbar() {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        toolbar.setBorder(BorderFactory.createEtchedBorder());
        
        // 搜索框
        toolbar.add(new JLabel("Search:"));
        searchField = new JTextField(20);
        searchField.setToolTipText("Search in URL, status, or response");
        searchField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            public void insertUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
            public void removeUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
            public void changedUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
        });
        toolbar.add(searchField);
        
        toolbar.add(Box.createHorizontalStrut(10));
        
        // 状态过滤器
        toolbar.add(new JLabel("Status:"));
        statusFilterCombo = new JComboBox<>(new String[]{
            "All", "2xx", "3xx", "4xx", "5xx", "Error"
        });
        statusFilterCombo.addActionListener(e -> applyFilters());
        toolbar.add(statusFilterCombo);
        
        toolbar.add(Box.createHorizontalStrut(10));
        
        // 方法过滤器
        toolbar.add(new JLabel("Method:"));
        methodFilterCombo = new JComboBox<>(new String[]{
            "All", "GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"
        });
        methodFilterCombo.addActionListener(e -> applyFilters());
        toolbar.add(methodFilterCombo);
        
        toolbar.add(Box.createHorizontalStrut(20));
        
        // 操作按钮
        clearButton = new JButton("Clear");
        clearButton.addActionListener(e -> clearResults());
        toolbar.add(clearButton);
        
        exportButton = new JButton("Export");
        exportButton.addActionListener(e -> exportResults());
        toolbar.add(exportButton);
        
        return toolbar;
    }
    
    /**
     * 创建结果表格 - JsRouteScan风格，支持排序
     */
    private void createResultTable() {
        tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 所有单元格不可编辑
            }

            @Override
            public Class<?> getColumnClass(int columnIndex) {
                switch (columnIndex) {
                    case 0: return Integer.class;  // # 列
                    case 3: return Integer.class;  // Status 列 - 用于数值排序
                    case 4: return Integer.class;  // Length 列 - 用于数值排序
                    case 5: return Long.class;     // Time 列 - 用于数值排序
                    default: return String.class;
                }
            }
        };

        resultTable = new JTable(tableModel);
        resultTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultTable.setAutoResizeMode(JTable.AUTO_RESIZE_SUBSEQUENT_COLUMNS);

        // ✅ 启用表格排序功能
        resultTable.setAutoCreateRowSorter(true);
        TableRowSorter<DefaultTableModel> tableSorter = new TableRowSorter<>(tableModel);
        resultTable.setRowSorter(tableSorter);

        // ✅ 自定义排序器 - 处理特殊格式的数据
        tableSorter.setComparator(4, new java.util.Comparator<Integer>() {
            @Override
            public int compare(Integer o1, Integer o2) {
                // Length列按数值大小排序
                return Integer.compare(o1, o2);
            }
        });

        tableSorter.setComparator(5, new java.util.Comparator<Long>() {
            @Override
            public int compare(Long o1, Long o2) {
                // Time列按数值大小排序
                return Long.compare(o1, o2);
            }
        });

        // 设置列宽 - 按照JsRouteScan的比例
        resultTable.getColumnModel().getColumn(0).setPreferredWidth(50);   // #
        resultTable.getColumnModel().getColumn(1).setPreferredWidth(400);  // URL
        resultTable.getColumnModel().getColumn(2).setPreferredWidth(80);   // Method
        resultTable.getColumnModel().getColumn(3).setPreferredWidth(80);   // Status
        resultTable.getColumnModel().getColumn(4).setPreferredWidth(80);   // Length
        resultTable.getColumnModel().getColumn(5).setPreferredWidth(100);  // Time
        resultTable.getColumnModel().getColumn(6).setPreferredWidth(200);  // Title

        // ✅ 设置自定义渲染器
        setupTableRenderer();
        
        // 添加行选择监听器
        resultTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showSelectedResult();
            }
        });
        
        // 添加右键菜单
        createContextMenu();
        
        // 添加双击监听器
        resultTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    sendToRepeater();
                }
            }
        });
    }
    
    /**
     * 创建HTTP消息查看器 - Repeater风格，Request和Response并排显示
     */
    private void createMessageViewer() {
        // ✅ 创建水平分割面板，像Repeater一样并排显示Request和Response
        JSplitPane messageSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        messageSplitPane.setResizeWeight(0.5); // 左右各占50%

        try {
            // 创建消息编辑器控制器
            IMessageEditorController controller = new IMessageEditorController() {
                @Override
                public IHttpService getHttpService() {
                    if (currentSelectedResult != null) {
                        try {
                            java.net.URL url = new java.net.URL(currentSelectedResult.getUrl());
                            boolean useHttps = "https".equals(url.getProtocol());
                            final int finalPort = url.getPort() == -1 ? (useHttps ? 443 : 80) : url.getPort();
                            // 创建简单的HttpService实现
                            return new IHttpService() {
                                @Override
                                public String getHost() { return url.getHost(); }
                                @Override
                                public int getPort() { return finalPort; }
                                @Override
                                public String getProtocol() { return useHttps ? "https" : "http"; }
                            };
                        } catch (Exception e) {
                            return null;
                        }
                    }
                    return null;
                }

                @Override
                public byte[] getRequest() {
                    return currentSelectedResult != null ? currentSelectedResult.getRequestBytes() : null;
                }

                @Override
                public byte[] getResponse() {
                    return currentSelectedResult != null ? currentSelectedResult.getResponseBytes() : null;
                }
            };

            // ✅ 创建请求查看器面板
            JPanel requestPanel = new JPanel(new BorderLayout());
            requestPanel.setBorder(BorderFactory.createTitledBorder("Request"));
            requestViewer = callbacks.createMessageEditor(controller, false);
            requestPanel.add(requestViewer.getComponent(), BorderLayout.CENTER);
            messageSplitPane.setLeftComponent(requestPanel);

            // ✅ 创建响应查看器面板
            JPanel responsePanel = new JPanel(new BorderLayout());
            responsePanel.setBorder(BorderFactory.createTitledBorder("Response"));
            responseViewer = callbacks.createMessageEditor(controller, false);
            responsePanel.add(responseViewer.getComponent(), BorderLayout.CENTER);
            messageSplitPane.setRightComponent(responsePanel);

        } catch (Exception e) {
            callbacks.printError("Failed to create message editors: " + e.getMessage());

            // 回退到简单的文本显示
            JPanel requestPanel = new JPanel(new BorderLayout());
            requestPanel.setBorder(BorderFactory.createTitledBorder("Request"));
            JTextArea requestArea = new JTextArea("Request viewer not available");
            requestArea.setEditable(false);
            requestPanel.add(new JScrollPane(requestArea), BorderLayout.CENTER);
            messageSplitPane.setLeftComponent(requestPanel);

            JPanel responsePanel = new JPanel(new BorderLayout());
            responsePanel.setBorder(BorderFactory.createTitledBorder("Response"));
            JTextArea responseArea = new JTextArea("Response viewer not available");
            responseArea.setEditable(false);
            responsePanel.add(new JScrollPane(responseArea), BorderLayout.CENTER);
            messageSplitPane.setRightComponent(responsePanel);
        }

        // ✅ 将分割面板作为消息查看器
        messageTabPane = new JTabbedPane();
        messageTabPane.addTab("HTTP Messages", messageSplitPane);
    }
    
    /**
     * 创建右键菜单 - JsRouteScan风格
     */
    private void createContextMenu() {
        JPopupMenu contextMenu = new JPopupMenu();
        
        JMenuItem sendToRepeaterItem = new JMenuItem("Send to Repeater");
        sendToRepeaterItem.addActionListener(e -> sendToRepeater());
        contextMenu.add(sendToRepeaterItem);
        
        JMenuItem copyUrlItem = new JMenuItem("Copy URL");
        copyUrlItem.addActionListener(e -> copySelectedUrl());
        contextMenu.add(copyUrlItem);
        
        contextMenu.addSeparator();
        
        JMenuItem deleteItem = new JMenuItem("Delete");
        deleteItem.addActionListener(e -> deleteSelected());
        contextMenu.add(deleteItem);
        
        resultTable.setComponentPopupMenu(contextMenu);
    }
    
    /**
     * 创建状态栏
     */
    private void createStatusBar() {
        statusLabel = new JLabel("Ready");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
    }
    
    /**
     * 扫描结果数据类 - 按照JsRouteScan的数据结构
     */
    public static class ScanResult {
        private int id;
        private String url;
        private String method;
        private int statusCode;
        private int contentLength;
        private long responseTime;
        private String title;
        private LocalDateTime scanTime;
        private byte[] requestBytes;
        private byte[] responseBytes;
        private String host;
        
        public ScanResult(int id, String url, String method) {
            this.id = id;
            this.url = url;
            this.method = method;
            this.scanTime = LocalDateTime.now();
            this.host = extractHost(url);
        }
        
        private String extractHost(String url) {
            try {
                if (url.startsWith("http://") || url.startsWith("https://")) {
                    java.net.URL urlObj = new java.net.URL(url);
                    String host = urlObj.getHost();
                    int port = urlObj.getPort();
                    if (port != -1 && port != 80 && port != 443) {
                        return host + ":" + port;
                    }
                    return host;
                }
                return "localhost";
            } catch (Exception e) {
                return "unknown";
            }
        }
        
        // Getter和Setter方法
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }
        
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        
        public int getStatusCode() { return statusCode; }
        public void setStatusCode(int statusCode) { this.statusCode = statusCode; }
        
        public int getContentLength() { return contentLength; }
        public void setContentLength(int contentLength) { this.contentLength = contentLength; }
        
        public long getResponseTime() { return responseTime; }
        public void setResponseTime(long responseTime) { this.responseTime = responseTime; }
        
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public LocalDateTime getScanTime() { return scanTime; }
        public void setScanTime(LocalDateTime scanTime) { this.scanTime = scanTime; }
        
        public byte[] getRequestBytes() { return requestBytes; }
        public void setRequestBytes(byte[] requestBytes) { this.requestBytes = requestBytes; }
        
        public byte[] getResponseBytes() { return responseBytes; }
        public void setResponseBytes(byte[] responseBytes) { this.responseBytes = responseBytes; }
        
        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }
    }
    
    /**
     * 添加扫描结果 - JsRouteScan风格，线程安全
     */
    public void addScanResult(ScanResult result) {
        // ✅ 确保数据操作在同步块中进行
        synchronized (scanResults) {
            scanResults.add(result);
        }

        // ✅ 直接调用UI更新方法（它们内部已经使用SwingUtilities.invokeLater）
        updateTableRow(result);
        updateStatusLabel();
    }

    /**
     * 从API测试结果转换为扫描结果
     */
    public void addApiTestResult(ApiTestResultTab.ApiTestResult apiResult) {
        ScanResult scanResult = new ScanResult(scanResults.size() + 1, apiResult.getUrl(), apiResult.getMethod());
        scanResult.setStatusCode(apiResult.getStatusCode());
        scanResult.setContentLength(apiResult.getContentLength());
        scanResult.setResponseTime(apiResult.getResponseTime());
        scanResult.setScanTime(apiResult.getTestTime());
        scanResult.setRequestBytes(apiResult.getRequestBytes());
        scanResult.setResponseBytes(apiResult.getResponseBytes());

        // 从响应中提取标题
        String title = extractTitleFromResponse(apiResult.getResponseBody());
        scanResult.setTitle(title);

        addScanResult(scanResult);
    }

    /**
     * 从响应中提取标题
     */
    private String extractTitleFromResponse(String responseBody) {
        if (responseBody == null || responseBody.isEmpty()) {
            return "";
        }

        // 尝试从HTML中提取title
        if (responseBody.contains("<title>")) {
            try {
                int start = responseBody.indexOf("<title>") + 7;
                int end = responseBody.indexOf("</title>", start);
                if (end > start) {
                    return responseBody.substring(start, end).trim();
                }
            } catch (Exception e) {
                // 忽略提取错误
            }
        }

        // 尝试从JSON中提取信息
        if (responseBody.trim().startsWith("{")) {
            if (responseBody.contains("\"message\"")) {
                try {
                    int start = responseBody.indexOf("\"message\"") + 10;
                    start = responseBody.indexOf("\"", start) + 1;
                    int end = responseBody.indexOf("\"", start);
                    if (end > start) {
                        return responseBody.substring(start, end);
                    }
                } catch (Exception e) {
                    // 忽略提取错误
                }
            }
            return "JSON Response";
        }

        return "Response";
    }

    /**
     * 更新表格行 - 支持排序的数据类型
     */
    private void updateTableRow(ScanResult result) {
        SwingUtilities.invokeLater(() -> {
            // ✅ 使用正确的数据类型以支持排序
            Object[] rowData = {
                result.getId(),                                    // Integer - 支持数值排序
                result.getUrl(),                                   // String - 支持字母排序
                result.getMethod(),                                // String - 支持字母排序
                result.getStatusCode() > 0 ? result.getStatusCode() : 0,  // Integer - 支持数值排序
                result.getContentLength() > 0 ? result.getContentLength() : 0,  // Integer - 支持数值排序
                result.getResponseTime(),                          // Long - 支持数值排序
                result.getTitle()                                  // String - 支持字母排序
            };

            tableModel.addRow(rowData);
        });
    }

    /**
     * 自定义表格渲染器 - 美化显示效果
     */
    private void setupTableRenderer() {
        // ✅ 状态码列的自定义渲染器
        resultTable.getColumnModel().getColumn(3).setCellRenderer(new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (value instanceof Integer) {
                    int status = (Integer) value;
                    if (status == 0) {
                        setText("Error");
                        setForeground(Color.RED);
                    } else {
                        setText(String.valueOf(status));
                        // 根据状态码设置颜色
                        if (status >= 200 && status < 300) {
                            setForeground(new Color(0, 128, 0)); // 绿色
                        } else if (status >= 300 && status < 400) {
                            setForeground(new Color(255, 165, 0)); // 橙色
                        } else if (status >= 400) {
                            setForeground(Color.RED); // 红色
                        } else {
                            setForeground(Color.BLACK);
                        }
                    }
                }

                if (isSelected) {
                    setForeground(Color.WHITE);
                }

                return c;
            }
        });

        // ✅ 长度列的自定义渲染器
        resultTable.getColumnModel().getColumn(4).setCellRenderer(new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (value instanceof Integer) {
                    int length = (Integer) value;
                    if (length == 0) {
                        setText("-");
                    } else {
                        setText(String.valueOf(length));
                    }
                }

                return c;
            }
        });

        // ✅ 时间列的自定义渲染器
        resultTable.getColumnModel().getColumn(5).setCellRenderer(new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (value instanceof Long) {
                    long time = (Long) value;
                    setText(time + "ms");
                }

                return c;
            }
        });
    }

    /**
     * 显示选中的结果 - 支持并排显示
     */
    private void showSelectedResult() {
        int selectedRow = resultTable.getSelectedRow();
        if (selectedRow >= 0) {
            // ✅ 处理排序后的行索引转换
            int modelRow = resultTable.convertRowIndexToModel(selectedRow);
            int resultId = (Integer) tableModel.getValueAt(modelRow, 0);

            // 查找对应的扫描结果
            currentSelectedResult = scanResults.stream()
                .filter(r -> r.getId() == resultId)
                .findFirst()
                .orElse(null);

            if (currentSelectedResult != null && requestViewer != null && responseViewer != null) {
                // ✅ 更新请求查看器
                if (currentSelectedResult.getRequestBytes() != null) {
                    requestViewer.setMessage(currentSelectedResult.getRequestBytes(), true);
                } else {
                    // 构建请求
                    String request = buildHttpRequest(currentSelectedResult);
                    requestViewer.setMessage(request.getBytes(), true);
                }

                // ✅ 更新响应查看器
                if (currentSelectedResult.getResponseBytes() != null) {
                    responseViewer.setMessage(currentSelectedResult.getResponseBytes(), false);
                } else {
                    // 构建响应
                    String response = buildHttpResponse(currentSelectedResult);
                    responseViewer.setMessage(response.getBytes(), false);
                }

                // ✅ 更新状态栏显示选中项的详细信息
                updateStatusForSelectedResult(currentSelectedResult);
            }
        } else {
            currentSelectedResult = null;
            if (requestViewer != null) {
                requestViewer.setMessage(new byte[0], true);
            }
            if (responseViewer != null) {
                responseViewer.setMessage(new byte[0], false);
            }
            updateStatusLabel();
        }
    }

    /**
     * 更新状态栏显示选中项的详细信息
     */
    private void updateStatusForSelectedResult(ScanResult result) {
        SwingUtilities.invokeLater(() -> {
            String statusText = String.format("Selected: %s %s - Status: %d - Length: %d bytes - Time: %dms",
                result.getMethod(),
                result.getUrl(),
                result.getStatusCode(),
                result.getContentLength(),
                result.getResponseTime()
            );
            statusLabel.setText(statusText);
        });
    }

    /**
     * 构建HTTP请求
     */
    private String buildHttpRequest(ScanResult result) {
        StringBuilder request = new StringBuilder();

        try {
            java.net.URL url = new java.net.URL(result.getUrl());
            String path = url.getPath();
            if (url.getQuery() != null) {
                path += "?" + url.getQuery();
            }

            request.append(result.getMethod()).append(" ").append(path).append(" HTTP/1.1\r\n");
            request.append("Host: ").append(url.getHost());
            if (url.getPort() != -1 && url.getPort() != 80 && url.getPort() != 443) {
                request.append(":").append(url.getPort());
            }
            request.append("\r\n");
            request.append("User-Agent: Burp API Checker\r\n");
            request.append("Accept: application/json, */*\r\n");
            request.append("Connection: close\r\n");
            request.append("\r\n");

        } catch (Exception e) {
            request.append(result.getMethod()).append(" ").append(result.getUrl()).append(" HTTP/1.1\r\n");
            request.append("User-Agent: Burp API Checker\r\n");
            request.append("Accept: */*\r\n");
            request.append("Connection: close\r\n");
            request.append("\r\n");
        }

        return request.toString();
    }

    /**
     * 构建HTTP响应
     */
    private String buildHttpResponse(ScanResult result) {
        StringBuilder response = new StringBuilder();

        response.append("HTTP/1.1 ").append(result.getStatusCode()).append(" ");
        response.append(getStatusMessage(result.getStatusCode())).append("\r\n");
        response.append("Content-Type: application/json\r\n");
        response.append("Content-Length: ").append(result.getContentLength()).append("\r\n");
        response.append("Server: Mock Server\r\n");
        response.append("\r\n");

        // 简单的响应体
        if (result.getStatusCode() == 200) {
            response.append("{\"status\":\"success\",\"message\":\"Request processed successfully\"}");
        } else if (result.getStatusCode() >= 400) {
            response.append("{\"error\":\"Error occurred\",\"code\":").append(result.getStatusCode()).append("}");
        }

        return response.toString();
    }

    /**
     * 获取状态消息
     */
    private String getStatusMessage(int statusCode) {
        switch (statusCode) {
            case 200: return "OK";
            case 201: return "Created";
            case 204: return "No Content";
            case 301: return "Moved Permanently";
            case 302: return "Found";
            case 400: return "Bad Request";
            case 401: return "Unauthorized";
            case 403: return "Forbidden";
            case 404: return "Not Found";
            case 500: return "Internal Server Error";
            default: return "Unknown";
        }
    }

    /**
     * 更新状态标签
     */
    private void updateStatusLabel() {
        SwingUtilities.invokeLater(() -> {
            int totalResults = scanResults.size();
            int visibleResults = resultTable.getRowCount();

            if (totalResults == visibleResults) {
                statusLabel.setText("Total: " + totalResults + " results");
            } else {
                statusLabel.setText("Showing: " + visibleResults + "/" + totalResults + " results");
            }
        });
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        applyFilters();
    }

    /**
     * 应用过滤器 - 支持排序功能
     */
    private void applyFilters() {
        String searchText = searchField.getText().trim().toLowerCase();
        String selectedStatus = (String) statusFilterCombo.getSelectedItem();
        String selectedMethod = (String) methodFilterCombo.getSelectedItem();

        // ✅ 获取当前的排序器（表格已经有自动创建的排序器）
        @SuppressWarnings("unchecked")
        TableRowSorter<DefaultTableModel> currentSorter = (TableRowSorter<DefaultTableModel>) resultTable.getRowSorter();

        if (searchText.isEmpty() && "All".equals(selectedStatus) && "All".equals(selectedMethod)) {
            // 清除过滤器，但保持排序功能
            if (currentSorter != null) {
                currentSorter.setRowFilter(null);
            }
            updateStatusLabel();
            return;
        }

        RowFilter<DefaultTableModel, Object> filter = new RowFilter<DefaultTableModel, Object>() {
            @Override
            public boolean include(Entry<? extends DefaultTableModel, ? extends Object> entry) {
                // ✅ 搜索过滤 - 处理不同数据类型
                if (!searchText.isEmpty()) {
                    String url = entry.getStringValue(1).toLowerCase();
                    String method = entry.getStringValue(2).toLowerCase();
                    String title = entry.getStringValue(6).toLowerCase();

                    // 处理状态码（可能是Integer类型）
                    String status = "";
                    Object statusObj = entry.getValue(3);
                    if (statusObj instanceof Integer) {
                        int statusCode = (Integer) statusObj;
                        status = statusCode == 0 ? "error" : String.valueOf(statusCode);
                    } else {
                        status = entry.getStringValue(3);
                    }
                    status = status.toLowerCase();

                    if (!url.contains(searchText) && !method.contains(searchText) &&
                        !status.contains(searchText) && !title.contains(searchText)) {
                        return false;
                    }
                }

                // ✅ 状态过滤 - 处理Integer类型的状态码
                if (!"All".equals(selectedStatus)) {
                    Object statusObj = entry.getValue(3);
                    if (!matchesStatusFilterObject(statusObj, selectedStatus)) {
                        return false;
                    }
                }

                // ✅ 方法过滤
                if (!"All".equals(selectedMethod)) {
                    if (!selectedMethod.equals(entry.getStringValue(2))) {
                        return false;
                    }
                }

                return true;
            }
        };

        // ✅ 应用过滤器到现有的排序器
        if (currentSorter != null) {
            currentSorter.setRowFilter(filter);
        }

        updateStatusLabel();
    }

    /**
     * 检查状态码对象是否匹配过滤器
     */
    private boolean matchesStatusFilterObject(Object statusObj, String filter) {
        if (statusObj instanceof Integer) {
            int code = (Integer) statusObj;
            if (code == 0) {
                return "Error".equals(filter);
            }

            switch (filter) {
                case "2xx": return code >= 200 && code < 300;
                case "3xx": return code >= 300 && code < 400;
                case "4xx": return code >= 400 && code < 500;
                case "5xx": return code >= 500;
                case "Error": return false;
                default: return true;
            }
        } else {
            // 回退到字符串匹配
            return matchesStatusFilter(statusObj.toString(), filter);
        }
    }

    /**
     * 检查状态码是否匹配过滤器
     */
    private boolean matchesStatusFilter(String status, String filter) {
        if ("Error".equals(status)) {
            return "Error".equals(filter);
        }

        try {
            int code = Integer.parseInt(status);
            switch (filter) {
                case "2xx": return code >= 200 && code < 300;
                case "3xx": return code >= 300 && code < 400;
                case "4xx": return code >= 400 && code < 500;
                case "5xx": return code >= 500;
                case "Error": return false;
                default: return true;
            }
        } catch (NumberFormatException e) {
            return "Error".equals(filter);
        }
    }

    /**
     * 清除结果
     */
    private void clearResults() {
        int result = JOptionPane.showConfirmDialog(
            mainPanel,
            "Are you sure you want to clear all results?",
            "Confirm Clear",
            JOptionPane.YES_NO_OPTION
        );

        if (result == JOptionPane.YES_OPTION) {
            scanResults.clear();
            tableModel.setRowCount(0);
            currentSelectedResult = null;
            if (requestViewer != null) {
                requestViewer.setMessage(new byte[0], true);
            }
            if (responseViewer != null) {
                responseViewer.setMessage(new byte[0], false);
            }
            updateStatusLabel();
        }
    }

    /**
     * 导出结果
     */
    private void exportResults() {
        if (scanResults.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "No results to export", "Info", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("Export Results");
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("CSV Files", "csv"));

        if (fileChooser.showSaveDialog(mainPanel) == JFileChooser.APPROVE_OPTION) {
            try {
                exportToCsv(fileChooser.getSelectedFile());
                JOptionPane.showMessageDialog(mainPanel, "Export successful!", "Info", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(mainPanel, "Export failed: " + e.getMessage(), "Error", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 导出到CSV文件
     */
    private void exportToCsv(java.io.File file) throws java.io.IOException {
        try (java.io.PrintWriter writer = new java.io.PrintWriter(new java.io.FileWriter(file))) {
            // 写入表头
            writer.println("ID,URL,Method,Status,Length,Time,Title,Host");

            // 写入数据
            for (ScanResult result : scanResults) {
                writer.printf("%d,\"%s\",\"%s\",%d,%d,%dms,\"%s\",\"%s\"%n",
                    result.getId(),
                    result.getUrl(),
                    result.getMethod(),
                    result.getStatusCode(),
                    result.getContentLength(),
                    result.getResponseTime(),
                    result.getTitle(),
                    result.getHost()
                );
            }
        }
    }

    /**
     * 发送到Repeater
     */
    private void sendToRepeater() {
        if (currentSelectedResult == null) {
            JOptionPane.showMessageDialog(mainPanel, "Please select a result first", "Info", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        try {
            java.net.URL url = new java.net.URL(currentSelectedResult.getUrl());
            boolean useHttps = "https".equals(url.getProtocol());
            int port = url.getPort();
            if (port == -1) {
                port = useHttps ? 443 : 80;
            }

            byte[] request;
            if (currentSelectedResult.getRequestBytes() != null) {
                request = currentSelectedResult.getRequestBytes();
            } else {
                request = buildHttpRequest(currentSelectedResult).getBytes();
            }

            callbacks.sendToRepeater(url.getHost(), port, useHttps, request);
            JOptionPane.showMessageDialog(mainPanel, "Sent to Repeater successfully", "Info", JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(mainPanel, "Failed to send to Repeater: " + e.getMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 复制选中的URL
     */
    private void copySelectedUrl() {
        if (currentSelectedResult != null) {
            java.awt.datatransfer.StringSelection selection = new java.awt.datatransfer.StringSelection(currentSelectedResult.getUrl());
            java.awt.Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);
            JOptionPane.showMessageDialog(mainPanel, "URL copied to clipboard", "Info", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * 删除选中项
     */
    private void deleteSelected() {
        int selectedRow = resultTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = resultTable.convertRowIndexToModel(selectedRow);
            int resultId = (Integer) tableModel.getValueAt(modelRow, 0);

            // 从数据中移除
            scanResults.removeIf(result -> result.getId() == resultId);

            // 从表格中移除
            tableModel.removeRow(modelRow);

            // 清除选择
            currentSelectedResult = null;
            if (requestViewer != null) {
                requestViewer.setMessage(new byte[0], true);
            }
            if (responseViewer != null) {
                responseViewer.setMessage(new byte[0], false);
            }

            updateStatusLabel();
        }
    }

    /**
     * 获取面板
     */
    public JPanel getPanel() {
        return mainPanel;
    }
}
