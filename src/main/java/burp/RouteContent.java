package burp;

import java.util.*;
import java.time.LocalDateTime;

/**
 * 路径内容管理类 - 参考JsRouteScan的路径管理设计
 * 管理发现的API路径和相关信息
 */
public class RouteContent {
    
    private String path;
    private String host;
    private Set<String> methods;
    private List<String> parameters;
    private String sourceFile;
    private String sourceType;
    private int statusCode;
    private String contentType;
    private LocalDateTime discoveryTime;
    private boolean isTested;
    private String responseBody;
    private long responseTime;
    private Map<String, String> headers;
    
    // 路径分析信息
    private boolean isApiEndpoint;
    private boolean hasParameters;
    private boolean isSecure;
    private String pathType;
    private int riskLevel;
    
    public RouteContent(String host, String path) {
        this.host = host;
        this.path = path;
        this.methods = new HashSet<>();
        this.parameters = new ArrayList<>();
        this.headers = new HashMap<>();
        this.discoveryTime = LocalDateTime.now();
        this.isTested = false;
        this.statusCode = -1;
        this.responseTime = -1;
        
        // 分析路径特征
        analyzePath();
    }
    
    public RouteContent(String host, String path, String sourceFile, String sourceType) {
        this(host, path);
        this.sourceFile = sourceFile;
        this.sourceType = sourceType;
    }
    
    /**
     * 分析路径特征
     */
    private void analyzePath() {
        String lowerPath = path.toLowerCase();
        
        // 判断是否为API端点
        this.isApiEndpoint = lowerPath.contains("/api/") || 
                            lowerPath.contains("/v1/") || 
                            lowerPath.contains("/v2/") ||
                            lowerPath.contains("/rest/") ||
                            lowerPath.contains("/graphql") ||
                            lowerPath.endsWith(".json") ||
                            lowerPath.endsWith(".xml");
        
        // 判断是否有参数
        this.hasParameters = path.contains("?") || path.contains("{") || path.contains(":");
        
        // 判断是否为安全路径
        this.isSecure = lowerPath.contains("/admin") || 
                       lowerPath.contains("/login") ||
                       lowerPath.contains("/auth") ||
                       lowerPath.contains("/password") ||
                       lowerPath.contains("/config");
        
        // 确定路径类型
        determinePathType();
        
        // 计算风险等级
        calculateRiskLevel();
    }
    
    /**
     * 确定路径类型
     */
    private void determinePathType() {
        String lowerPath = path.toLowerCase();
        
        if (lowerPath.contains("/api/")) {
            pathType = "API";
        } else if (lowerPath.contains("/admin")) {
            pathType = "管理";
        } else if (lowerPath.contains("/auth") || lowerPath.contains("/login")) {
            pathType = "认证";
        } else if (lowerPath.endsWith(".js")) {
            pathType = "脚本";
        } else if (lowerPath.endsWith(".json") || lowerPath.endsWith(".xml")) {
            pathType = "数据";
        } else if (lowerPath.contains("/upload") || lowerPath.contains("/file")) {
            pathType = "文件";
        } else {
            pathType = "页面";
        }
    }
    
    /**
     * 计算风险等级 (1-5, 5为最高)
     */
    private void calculateRiskLevel() {
        riskLevel = 1; // 基础风险
        
        if (isSecure) riskLevel += 2;
        if (isApiEndpoint) riskLevel += 1;
        if (hasParameters) riskLevel += 1;
        if (path.toLowerCase().contains("delete") || 
            path.toLowerCase().contains("remove")) riskLevel += 1;
        
        // 确保风险等级在1-5范围内
        riskLevel = Math.min(5, Math.max(1, riskLevel));
    }
    
    /**
     * 添加HTTP方法
     */
    public void addMethod(String method) {
        if (method != null && !method.trim().isEmpty()) {
            methods.add(method.toUpperCase());
        }
    }
    
    /**
     * 添加参数
     */
    public void addParameter(String parameter) {
        if (parameter != null && !parameter.trim().isEmpty() && !parameters.contains(parameter)) {
            parameters.add(parameter);
            hasParameters = true;
        }
    }
    
    /**
     * 设置测试结果
     */
    public void setTestResult(int statusCode, long responseTime, String contentType, String responseBody) {
        this.statusCode = statusCode;
        this.responseTime = responseTime;
        this.contentType = contentType;
        this.responseBody = responseBody;
        this.isTested = true;
    }
    
    /**
     * 添加响应头
     */
    public void addResponseHeader(String name, String value) {
        headers.put(name, value);
    }
    
    /**
     * 获取完整URL
     */
    public String getFullUrl() {
        return "http://" + host + path;
    }
    
    /**
     * 获取安全URL (HTTPS)
     */
    public String getSecureUrl() {
        return "https://" + host + path;
    }
    
    /**
     * 获取方法列表字符串
     */
    public String getMethodsString() {
        if (methods.isEmpty()) {
            return "未知";
        }
        return String.join(", ", methods);
    }
    
    /**
     * 获取参数列表字符串
     */
    public String getParametersString() {
        if (parameters.isEmpty()) {
            return "无";
        }
        return String.join(", ", parameters);
    }
    
    /**
     * 获取风险等级描述
     */
    public String getRiskLevelDescription() {
        switch (riskLevel) {
            case 1: return "很低";
            case 2: return "低";
            case 3: return "中";
            case 4: return "高";
            case 5: return "很高";
            default: return "未知";
        }
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (!isTested) {
            return "未测试";
        }
        
        if (statusCode >= 200 && statusCode < 300) {
            return "成功";
        } else if (statusCode >= 300 && statusCode < 400) {
            return "重定向";
        } else if (statusCode >= 400 && statusCode < 500) {
            return "客户端错误";
        } else if (statusCode >= 500) {
            return "服务器错误";
        } else {
            return "未知";
        }
    }
    
    // Getter和Setter方法
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
        analyzePath(); // 重新分析路径
    }
    
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public Set<String> getMethods() {
        return methods;
    }
    
    public List<String> getParameters() {
        return parameters;
    }
    
    public String getSourceFile() {
        return sourceFile;
    }
    
    public void setSourceFile(String sourceFile) {
        this.sourceFile = sourceFile;
    }
    
    public String getSourceType() {
        return sourceType;
    }
    
    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }
    
    public int getStatusCode() {
        return statusCode;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public LocalDateTime getDiscoveryTime() {
        return discoveryTime;
    }
    
    public boolean isTested() {
        return isTested;
    }
    
    public String getResponseBody() {
        return responseBody;
    }
    
    public long getResponseTime() {
        return responseTime;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public boolean isApiEndpoint() {
        return isApiEndpoint;
    }
    
    public boolean isHasParameters() {
        return hasParameters;
    }
    
    public boolean isSecure() {
        return isSecure;
    }
    
    public String getPathType() {
        return pathType;
    }
    
    public int getRiskLevel() {
        return riskLevel;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        RouteContent that = (RouteContent) obj;
        return Objects.equals(host, that.host) && Objects.equals(path, that.path);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(host, path);
    }
    
    @Override
    public String toString() {
        return String.format("RouteContent{host='%s', path='%s', type='%s', risk=%d, tested=%s}", 
            host, path, pathType, riskLevel, isTested);
    }
}
