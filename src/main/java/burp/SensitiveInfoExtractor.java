package burp;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.datatransfer.Clipboard;
import java.io.PrintWriter;
import java.net.URL;
import java.util.*;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 敏感信息提取器 - 基于BurpAPIFinder的设计思路
 * 支持多种敏感信息类型的检测和提取
 */
public class SensitiveInfoExtractor {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    
    // UI组件
    private JPanel mainPanel;
    private JTextField urlField;
    private JButton analyzeButton;
    private JCheckBox autoAnalyzeJSCheckBox;
    private JCheckBox realTimeAnalysisCheckBox;
    private JTextArea outputArea;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    
    // 数据存储
    private Map<String, List<String>> sensitiveData = new HashMap<>();
    private Map<String, String> sensitiveDataSources = new HashMap<>();
    private Set<String> processedUrls = new HashSet<>();
    
    // 移除重复的规则定义，使用统一配置
    // private static final SensitivePattern[] SENSITIVE_PATTERNS = { ... }; // 已删除
    // private static class SensitivePattern { ... }; // 已删除
    
    public SensitiveInfoExtractor(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        initializeUI();
        initializeData();
    }
    
    /**
     * 初始化UI界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建配置面板
        JPanel configPanel = createConfigPanel();
        mainPanel.add(configPanel, BorderLayout.NORTH);
        
        // 创建结果显示面板
        JPanel resultPanel = createResultPanel();
        mainPanel.add(resultPanel, BorderLayout.CENTER);
        
        // 创建状态面板
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        // 显示初始说明
        showWelcomeMessage();
    }
    
    /**
     * 创建配置面板
     */
    private JPanel createConfigPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(BorderFactory.createTitledBorder("敏感信息提取配置"));
        
        // URL输入区域
        JPanel urlPanel = new JPanel(new BorderLayout(5, 5));
        urlPanel.add(new JLabel("目标URL:"), BorderLayout.WEST);
        
        urlField = new JTextField();
        urlField.setToolTipText("输入要分析的网站URL，支持HTTP/HTTPS");
        urlPanel.add(urlField, BorderLayout.CENTER);
        
        analyzeButton = new JButton("开始分析");
        analyzeButton.addActionListener(e -> startAnalysis());
        urlPanel.add(analyzeButton, BorderLayout.EAST);
        
        // 选项区域
        JPanel optionsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        
        autoAnalyzeJSCheckBox = new JCheckBox("自动分析JS文件", true);
        autoAnalyzeJSCheckBox.setToolTipText("自动发现并分析页面中引用的JavaScript文件");
        optionsPanel.add(autoAnalyzeJSCheckBox);
        
        realTimeAnalysisCheckBox = new JCheckBox("实时监控", false);
        realTimeAnalysisCheckBox.setToolTipText("实时监控Burp代理流量中的敏感信息");
        realTimeAnalysisCheckBox.addActionListener(e -> toggleRealTimeAnalysis());
        optionsPanel.add(realTimeAnalysisCheckBox);
        
        panel.add(urlPanel);
        panel.add(Box.createVerticalStrut(5));
        panel.add(optionsPanel);
        
        return panel;
    }
    
    /**
     * 创建结果显示面板
     */
    private JPanel createResultPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("分析结果"));
        
        // 输出区域
        outputArea = new JTextArea();
        outputArea.setEditable(false);
        outputArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        outputArea.setBackground(new Color(248, 248, 248));
        
        JScrollPane scrollPane = new JScrollPane(outputArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        scrollPane.setPreferredSize(new Dimension(800, 400));
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 控制按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton clearButton = new JButton("清除结果");
        clearButton.addActionListener(e -> clearResults());
        buttonPanel.add(clearButton);
        
        JButton exportButton = new JButton("导出结果");
        exportButton.addActionListener(e -> exportResults());
        buttonPanel.add(exportButton);
        
        JButton copyButton = new JButton("复制结果");
        copyButton.addActionListener(e -> copyResults());
        buttonPanel.add(copyButton);
        
        JButton saveConfigButton = new JButton("保存配置");
        saveConfigButton.addActionListener(e -> saveConfiguration());
        buttonPanel.add(saveConfigButton);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * 创建状态面板
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        panel.add(statusLabel, BorderLayout.WEST);
        
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setString("等待开始...");
        panel.add(progressBar, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 初始化数据结构
     */
    private void initializeData() {
        // 初始化敏感数据存储
        for (SensitiveInfoConfig.SensitivePattern pattern : SensitiveInfoConfig.SENSITIVE_PATTERNS) {
            sensitiveData.put(pattern.name, new ArrayList<>());
        }
    }
    
    /**
     * 显示欢迎信息 - 使用统一配置动态生成分类
     */
    private void showWelcomeMessage() {
        outputArea.append("[信息] 敏感信息提取工具\n");
        outputArea.append(createSeparator("=", 50) + "\n");
        outputArea.append("支持检测的敏感信息类型:\n\n");
        
        // 使用统一配置动态生成分类显示
        Map<String, List<String>> categories = SensitiveInfoConfig.getCategories();
        for (Map.Entry<String, List<String>> entry : categories.entrySet()) {
            outputArea.append(entry.getKey() + ":\n");
            for (String type : entry.getValue()) {
                SensitiveInfoConfig.SensitivePattern pattern = SensitiveInfoConfig.getPatternByName(type);
                String riskInfo = pattern != null ? " (" + SensitiveInfoConfig.getRiskDescription(pattern.riskLevel) + ")" : "";
                outputArea.append("  • " + type + riskInfo + "\n");
            }
            outputArea.append("\n");
        }
        
        outputArea.append("使用方法:\n");
        outputArea.append("1. 输入目标URL\n");
        outputArea.append("2. 选择分析选项\n");
        outputArea.append("3. 点击'开始分析'按钮\n");
        outputArea.append("4. 查看检测结果\n\n");
        outputArea.append("提示: 勾选'实时监控'可自动分析Burp代理流量\n");
        outputArea.append(createSeparator("=", 50) + "\n\n");
    }
    
    /**
     * 创建分隔符字符串
     */
    private String createSeparator(String character, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(character);
        }
        return sb.toString();
    }
    
    /**
     * 开始分析
     */
    private void startAnalysis() {
        String url = urlField.getText().trim();
        if (url.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请输入目标URL", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // URL格式检查和修正
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
            urlField.setText(url);
        }
        
        final String targetUrl = url;
        
        // 禁用按钮，开始分析
        analyzeButton.setEnabled(false);
        clearResults();
        updateStatus("正在分析: " + targetUrl);
        
        // 后台线程执行分析
        new Thread(() -> {
            try {
                performAnalysis(targetUrl);
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    appendToOutput("❌ 分析失败: " + e.getMessage() + "\n");
                    updateStatus("分析失败");
                });
                callbacks.printError("Analysis failed: " + e.getMessage());
            } finally {
                SwingUtilities.invokeLater(() -> {
                    analyzeButton.setEnabled(true);
                    progressBar.setValue(0);
                    progressBar.setString("分析完成");
                });
            }
        }).start();
    }
    
    /**
     * 执行具体的分析逻辑
     */
    private void performAnalysis(String targetUrl) {
        try {
                         SwingUtilities.invokeLater(() -> {
                 appendToOutput("[开始] 开始分析: " + targetUrl + "\n");
                 appendToOutput("时间: " + new Date() + "\n");
                 appendToOutput(createSeparator("-", 50) + "\n");
                 progressBar.setValue(10);
                 progressBar.setString("正在获取主页面...");
             });
            
            // 1. 获取主页面内容
            String mainPageContent = fetchPageContent(targetUrl);
            if (mainPageContent != null) {
                SwingUtilities.invokeLater(() -> {
                    appendToOutput("[成功] 主页面获取成功 (" + mainPageContent.length() + " 字符)\n");
                    progressBar.setValue(30);
                    progressBar.setString("正在分析主页面...");
                });
                
                // 分析主页面
                scanSensitiveInfo(mainPageContent, "[主页面] " + targetUrl);
                
                // 2. 如果启用了JS分析，查找并分析JS文件
                if (autoAnalyzeJSCheckBox.isSelected()) {
                    SwingUtilities.invokeLater(() -> {
                        progressBar.setValue(50);
                        progressBar.setString("正在查找JS文件...");
                    });
                    
                    Set<String> jsUrls = extractJavaScriptUrls(mainPageContent, targetUrl);
                    
                    SwingUtilities.invokeLater(() -> {
                        appendToOutput("[信息] 发现 " + jsUrls.size() + " 个JavaScript文件\n");
                        progressBar.setValue(60);
                        progressBar.setString("正在分析JS文件...");
                    });
                    
                    // 分析JS文件
                    analyzeJavaScriptFiles(jsUrls);
                }
                
                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(90);
                    progressBar.setString("正在生成报告...");
                });
                
                // 3. 生成分析报告
                generateReport();
                
            } else {
                SwingUtilities.invokeLater(() -> {
                    appendToOutput("❌ 无法获取页面内容\n");
                    updateStatus("获取页面失败");
                });
            }
            
        } catch (Exception e) {
            SwingUtilities.invokeLater(() -> {
                appendToOutput("❌ 分析过程中出错: " + e.getMessage() + "\n");
            });
            throw e;
        }
    }
    
    /**
     * 获取页面内容
     */
    private String fetchPageContent(String url) {
        try {
            URL targetUrl = new URL(url);
            IHttpService httpService = helpers.buildHttpService(
                targetUrl.getHost(), 
                getEffectivePort(targetUrl), 
                targetUrl.getProtocol()
            );
            
            byte[] request = helpers.buildHttpRequest(targetUrl);
            IHttpRequestResponse response = callbacks.makeHttpRequest(httpService, request);
            
            if (response.getResponse() != null) {
                return new String(response.getResponse());
            }
        } catch (Exception e) {
            callbacks.printError("Failed to fetch page content: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 扫描内容中的敏感信息 - 使用统一配置
     */
    private void scanSensitiveInfo(String content, String source) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }
        
        int foundCount = 0;
        
        // 使用统一的敏感信息规则
        for (SensitiveInfoConfig.SensitivePattern sensitivePattern : SensitiveInfoConfig.SENSITIVE_PATTERNS) {
            Matcher matcher = sensitivePattern.pattern.matcher(content);
            java.util.List<String> findings = sensitiveData.get(sensitivePattern.name);
            if (findings == null) {
                findings = new ArrayList<>();
                sensitiveData.put(sensitivePattern.name, findings);
            }
            
            while (matcher.find()) {
                String match = matcher.group(1);
                if (match == null) {
                    match = matcher.group(0);
                }
                
                String cleanMatch = cleanAndValidate(match, sensitivePattern.name);
                if (cleanMatch != null && !findings.contains(cleanMatch)) {
                    findings.add(cleanMatch);
                    sensitiveDataSources.put(cleanMatch, source);
                    foundCount++;
                    
                    String riskInfo = SensitiveInfoConfig.getRiskDescription(sensitivePattern.riskLevel);
                    appendToOutput("发现 " + sensitivePattern.name + " (" + riskInfo + "): " + cleanMatch + "\n");
                    appendToOutput("  来源: " + source + "\n");
                    appendToOutput("  描述: " + sensitivePattern.description + "\n\n");
                }
            }
        }
        
        if (foundCount > 0) {
            appendToOutput("本次扫描发现 " + foundCount + " 项敏感信息\n");
            appendToOutput(createSeparator("-", 30) + "\n");
        }
    }
    
    /**
     * 清理和验证匹配结果
     */
    private String cleanAndValidate(String match, String type) {
        if (match == null || match.trim().isEmpty()) {
            return null;
        }
        
        match = match.trim();
        
        // 根据类型进行特殊处理
        switch (type) {
            case "邮箱地址":
                // 过滤示例邮箱
                if (match.contains("example.com") || match.contains("test.com") || 
                    match.contains("domain.com") || match.contains("localhost")) {
                    return null;
                }
                break;
                
            case "密码配置":
                // 过滤明显的示例密码
                String lower = match.toLowerCase();
                if (lower.contains("example") || lower.contains("test") || 
                    lower.contains("demo") || lower.equals("password") || 
                    lower.equals("123456") || lower.length() < 6) {
                    return null;
                }
                break;
                
            case "IPv4地址":
                // 过滤明显的示例IP
                if (match.equals("127.0.0.1") || match.equals("0.0.0.0") || 
                    match.equals("***************") || match.startsWith("127.0.0.")) {
                    return null;
                }
                break;
                
            case "JWT Token":
                // JWT至少要包含两个点
                if (!match.contains(".") || match.split("\\.").length < 3) {
                    return null;
                }
                break;
        }
        
        // 过滤太短的结果
        if (match.length() < 3) {
            return null;
        }
        
        return match;
    }
    
    /**
     * 提取JavaScript文件URLs
     */
    private Set<String> extractJavaScriptUrls(String htmlContent, String baseUrl) {
        Set<String> jsUrls = new HashSet<>();
        
        // 查找外部JS文件引用
        Pattern scriptPattern = Pattern.compile("<script[^>]+src=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher matcher = scriptPattern.matcher(htmlContent);
        
        while (matcher.find()) {
            String src = matcher.group(1);
            if (src.endsWith(".js") || src.contains(".js?")) {
                try {
                    if (src.startsWith("http")) {
                        jsUrls.add(src);
                    } else {
                        URL base = new URL(baseUrl);
                        URL jsUrl = new URL(base, src);
                        jsUrls.add(jsUrl.toString());
                    }
                } catch (Exception e) {
                    // 忽略无效URL
                }
            }
        }
        
        return jsUrls;
    }
    
    /**
     * 分析JavaScript文件
     */
    private void analyzeJavaScriptFiles(Set<String> jsUrls) {
        int count = 0;
        for (String jsUrl : jsUrls) {
            count++;
            final int currentCount = count;
            final int total = jsUrls.size();
            
            SwingUtilities.invokeLater(() -> {
                appendToOutput("📄 分析JS文件 (" + currentCount + "/" + total + "): " + jsUrl + "\n");
                progressBar.setValue(60 + (currentCount * 20 / total));
                progressBar.setString("分析JS文件 " + currentCount + "/" + total);
            });
            
            String jsContent = fetchPageContent(jsUrl);
            if (jsContent != null) {
                scanSensitiveInfo(jsContent, "[外部JS] " + jsUrl);
            }
            
            // 避免请求过快
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 生成分析报告 - 使用统一配置
     */
    private void generateReport() {
        SwingUtilities.invokeLater(() -> {
            appendToOutput("\n" + createSeparator("=", 50) + "\n");
            appendToOutput("[报告] 分析报告\n");
            appendToOutput(createSeparator("=", 50) + "\n");
            
            int totalFindings = 0;
            Map<String, Integer> categoryStats = new HashMap<>();
            
            // 按分类统计
            for (SensitiveInfoConfig.SensitivePattern pattern : SensitiveInfoConfig.SENSITIVE_PATTERNS) {
                List<String> findings = sensitiveData.get(pattern.name);
                if (findings != null && !findings.isEmpty()) {
                    int count = findings.size();
                    totalFindings += count;
                    categoryStats.merge(pattern.category, count, Integer::sum);
                    
                    appendToOutput("\n" + pattern.name + " (" + SensitiveInfoConfig.getRiskDescription(pattern.riskLevel) + "):\n");
                    for (String finding : findings) {
                        String source = sensitiveDataSources.get(finding);
                        appendToOutput("  • " + finding + (source != null ? " (来源: " + source + ")" : "") + "\n");
                    }
                }
            }
            
            // 分类统计摘要
            appendToOutput("\n" + createSeparator("-", 30) + "\n");
            appendToOutput("分类统计:\n");
            for (Map.Entry<String, Integer> entry : categoryStats.entrySet()) {
                appendToOutput("  " + entry.getKey() + ": " + entry.getValue() + " 项\n");
            }
            
            appendToOutput("\n总计发现: " + totalFindings + " 项敏感信息\n");
            if (totalFindings > 0) {
                appendToOutput("建议: 请检查这些敏感信息是否应该暴露在前端代码中\n");
            }
            
            appendToOutput(createSeparator("=", 50) + "\n");
        });
    }
    
    /**
     * 切换实时分析
     */
    private void toggleRealTimeAnalysis() {
        if (realTimeAnalysisCheckBox.isSelected()) {
            appendToOutput("[信息] 实时监控已启用\n");
            updateStatus("实时监控中...");
        } else {
            appendToOutput("[信息] 实时监控已停用\n");
            updateStatus("就绪");
        }
    }
    
    /**
     * 处理HTTP消息 - 用于实时分析
     */
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        if (!realTimeAnalysisCheckBox.isSelected() || messageIsRequest) {
            return;
        }
        
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
            String url = requestInfo.getUrl().toString();
            
            // 避免重复分析同一个URL
            if (processedUrls.contains(url)) {
                return;
            }
            processedUrls.add(url);
            
            byte[] response = messageInfo.getResponse();
            if (response != null) {
                String responseString = new String(response);
                
                // 异步分析以避免阻塞
                new Thread(() -> {
                    scanSensitiveInfo(responseString, "[实时监控] " + url);
                }).start();
            }
        } catch (Exception e) {
            callbacks.printError("Real-time analysis error: " + e.getMessage());
        }
    }
    
    // 辅助方法
    private void clearResults() {
        outputArea.setText("");
        sensitiveData.clear();
        sensitiveDataSources.clear();
        processedUrls.clear();
        initializeData();
        showWelcomeMessage();
    }
    
    private void updateStatus(String status) {
        statusLabel.setText(status);
    }
    
    private void appendToOutput(String text) {
        outputArea.append(text);
        outputArea.setCaretPosition(outputArea.getDocument().getLength());
    }
    
    private void exportResults() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出敏感信息分析结果");
        
        if (fileChooser.showSaveDialog(mainPanel) == JFileChooser.APPROVE_OPTION) {
            try {
                String filename = fileChooser.getSelectedFile().getAbsolutePath();
                if (!filename.endsWith(".txt")) {
                    filename += ".txt";
                }
                
                try (PrintWriter writer = new PrintWriter(filename, "UTF-8")) {
                    writer.print(outputArea.getText());
                }
                
                JOptionPane.showMessageDialog(mainPanel, "结果已导出到: " + filename, 
                    "导出成功", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(mainPanel, "导出失败: " + e.getMessage(), 
                    "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    private void copyResults() {
        String text = outputArea.getText();
        if (!text.trim().isEmpty()) {
            StringSelection selection = new StringSelection(text);
            Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            clipboard.setContents(selection, null);
            
            JOptionPane.showMessageDialog(mainPanel, "结果已复制到剪贴板", 
                "复制成功", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    private void saveConfiguration() {
        // 保存用户配置（URL历史、选项设置等）
        JOptionPane.showMessageDialog(mainPanel, "配置保存功能开发中", 
            "提示", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private int getEffectivePort(URL url) {
        int port = url.getPort();
        if (port == -1) {
            port = url.getProtocol().equals("https") ? 443 : 80;
        }
        return port;
    }
    
    // Getter方法
    public JPanel getPanel() {
        return mainPanel;
    }
    
    public String getTabCaption() {
        return "敏感信息提取";
    }
} 