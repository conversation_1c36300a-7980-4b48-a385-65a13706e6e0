package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.*;
import java.util.List;
import java.net.URL;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

// Main Burp extension class that implements the required interfaces
public class BurpExtender implements IBurpExtender, ITab, IHttpListener {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private JPanel mainPanel;
    private JTabbedPane mainTabbedPane;
    private JTextArea outputArea;
    private JTextField urlField;
    private JTextArea customHeadersField;
    private JComboBox<String> attackTypeBox;
    private JComboBox<String> httpMethodBox;
    private JButton startButton;
    
    // Manual API testing controls
    private JTextField apiPathsField;
    private JButton testButton;
    
    // Domain filtering components
    private JTextField domainFilterField;
    private JCheckBox enableDomainFilterCheckBox;
    private JButton clearFilterButton;
    private Set<String> domainFilters = new HashSet<>();
    
    // Domain list components
    private DefaultListModel<String> domainListModel;
    private JList<String> domainList;
    private Set<String> discoveredDomains = new HashSet<>();
    private JLabel domainCountLabel;
    
    // Sensitive info components
    private JTextField sensitiveUrlField;
    private JButton sensitiveAnalyzeButton;
    private JCheckBox autoAnalyzeCheckBox;
    private JCheckBox syncAnalyzeCheckBox;
    
    // Data collections
    private Set<String> apiPaths = new HashSet<>();
    private Set<String> jsUrls = new HashSet<>();
    private Map<String, List<String>> sensitiveData = new HashMap<>();
    private Map<String, String> sensitiveDataSources = new HashMap<>();
    
    // Scan results storage for filtering
    private java.util.List<ScanResult> allScanResults = new java.util.ArrayList<>();
    
    // Scan result class
    private static class ScanResult {
        public final String type;
        public final String content;
        public final String sourceUrl;
        public final long timestamp;
        
        public ScanResult(String type, String content, String sourceUrl) {
            this.type = type;
            this.content = content;
            this.sourceUrl = sourceUrl;
            this.timestamp = System.currentTimeMillis();
        }
    }
    
    // Sensitive info patterns (simplified)
    private static final String[] SENSITIVE_TYPES = {
        "API密钥", "数据库信息", "身份信息", "网络信息", "加密信息"
    };
    
    private static final Pattern[] SENSITIVE_PATTERNS = {
        Pattern.compile("(?i)(api[_-]?key|apikey|access[_-]?token|secret[_-]?key)\\s*[:=]\\s*['\"]([^'\"\\s]{16,})['\"]"),
        Pattern.compile("(?i)(password|passwd|pwd)\\s*[:=]\\s*['\"]([^'\"\\s]{6,})['\"]"),
        Pattern.compile("\\b(?:\\d{4}[- ]?){3}\\d{4}\\b"), // Credit card pattern
        Pattern.compile("\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b"), // IP address
        Pattern.compile("-----BEGIN [A-Z ]+-----[\\s\\S]*?-----END [A-Z ]+-----") // Private key
    };

    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        
        callbacks.setExtensionName("API Security Checker");
        callbacks.registerHttpListener(this);
        
        // Create UI
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                createUI();
                callbacks.addSuiteTab(BurpExtender.this);
            }
        });
        
        callbacks.printOutput("API Security Checker extension loaded successfully!");
    }
    
    @Override
    public String getTabCaption() {
        return "API Checker";
    }
    
    @Override
    public Component getUiComponent() {
        return mainPanel;
    }
    
    @Override
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        if (!messageIsRequest) {
            try {
                IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
                URL url = requestInfo.getUrl();
                String urlString = url.toString();
                
                // Check domain filter
                if (!matchesDomainFilter(urlString)) {
                    return;
                }
                
                // Collect domains
                addDiscoveredDomain(urlString);
                
                // Look for JavaScript files
                if (urlString.endsWith(".js")) {
                    jsUrls.add(urlString);
                    addScanResult("JS Found", urlString, urlString);
                }
                
                // Look for API patterns in response
                byte[] response = messageInfo.getResponse();
                if (response != null) {
                    String responseString = new String(response);
                    findApiPaths(responseString, url.toString());
                    
                    // Extract sensitive info if sync analysis is enabled
                    if (syncAnalyzeCheckBox != null && syncAnalyzeCheckBox.isSelected()) {
                        extractSensitiveInfo(responseString, urlString);
                    }
                }
            } catch (Exception e) {
                callbacks.printError("Error processing HTTP message: " + e.getMessage());
            }
        }
    }
    
    private void createUI() {
        mainPanel = new JPanel();
        mainPanel.setLayout(new BorderLayout());
        
        mainTabbedPane = new JTabbedPane();
        
        // Create API scan panel
        JPanel apiScanPanel = createApiScanPanel();
        mainTabbedPane.addTab("API扫描", apiScanPanel);
        
        // Create sensitive info panel
        JPanel sensitivePanel = createSensitiveInfoPanel();
        mainTabbedPane.addTab("敏感信息提取", sensitivePanel);
        
        mainPanel.add(mainTabbedPane, BorderLayout.CENTER);
        
        callbacks.customizeUiComponent(mainPanel);
    }
    
    private JPanel createApiScanPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // Config panel
        JPanel configPanel = new JPanel();
        configPanel.setLayout(new BoxLayout(configPanel, BoxLayout.Y_AXIS));
        configPanel.setBorder(BorderFactory.createTitledBorder("Configuration"));
        
        // Basic config
        JPanel basicConfigPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Target URL
        gbc.gridx = 0; gbc.gridy = 0;
        basicConfigPanel.add(new JLabel("Target URL:"), gbc);
        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        urlField = new JTextField("https://example.com");
        basicConfigPanel.add(urlField, gbc);
        
        // Attack Type
        gbc.gridx = 0; gbc.gridy = 1; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        basicConfigPanel.add(new JLabel("Attack Type:"), gbc);
        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        attackTypeBox = new JComboBox<>(new String[]{"Collect Only", "Collect + Test"});
        basicConfigPanel.add(attackTypeBox, gbc);
        
        // Sync analysis checkbox
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        syncAnalyzeCheckBox = new JCheckBox("同时提取敏感信息", true);
        basicConfigPanel.add(syncAnalyzeCheckBox, gbc);
        
        // Domain filtering
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 1; gbc.weightx = 0;
        enableDomainFilterCheckBox = new JCheckBox("启用域名过滤", false);
        enableDomainFilterCheckBox.addActionListener(e -> {
            boolean enabled = enableDomainFilterCheckBox.isSelected();
            domainFilterField.setEnabled(enabled);
            clearFilterButton.setEnabled(enabled);
        });
        basicConfigPanel.add(enableDomainFilterCheckBox, gbc);
        
        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        domainFilterField = new JTextField("*.example.com");
        domainFilterField.setEnabled(false);
        domainFilterField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            public void insertUpdate(javax.swing.event.DocumentEvent e) { updateDomainFilters(); }
            public void removeUpdate(javax.swing.event.DocumentEvent e) { updateDomainFilters(); }
            public void changedUpdate(javax.swing.event.DocumentEvent e) { updateDomainFilters(); }
        });
        basicConfigPanel.add(domainFilterField, gbc);
        
        gbc.gridx = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        clearFilterButton = new JButton("清空");
        clearFilterButton.setEnabled(false);
        clearFilterButton.addActionListener(e -> {
            domainFilterField.setText("");
            updateDomainFilters();
        });
        basicConfigPanel.add(clearFilterButton, gbc);
        
        // Start button
        gbc.gridx = 0; gbc.gridy = 4; gbc.gridwidth = 3; gbc.anchor = GridBagConstraints.CENTER;
        startButton = new JButton("Start Scan");
        startButton.addActionListener(e -> startScan());
        basicConfigPanel.add(startButton, gbc);
        
        configPanel.add(basicConfigPanel);
        
        // Output panel with split layout
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setResizeWeight(0.75);
        
        // Left side - Output area
        outputArea = new JTextArea();
        outputArea.setEditable(false);
        outputArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane outputScrollPane = new JScrollPane(outputArea);
        splitPane.setLeftComponent(outputScrollPane);
        
        // Right side - Domain list panel
        JPanel domainPanel = createDomainListPanel();
        splitPane.setRightComponent(domainPanel);
        
        // Control panel
        JPanel controlPanel = new JPanel(new FlowLayout());
        JButton clearButton = new JButton("Clear Output");
        clearButton.addActionListener(e -> clearAllResults());
        controlPanel.add(clearButton);
        
        JButton applyFilterButton = new JButton("应用筛选");
        applyFilterButton.addActionListener(e -> filterAndDisplayResults());
        controlPanel.add(applyFilterButton);
        
        JButton exportButton = new JButton("Export Results");
        exportButton.addActionListener(e -> exportResults());
        controlPanel.add(exportButton);
        
        JPanel outputPanel = new JPanel(new BorderLayout());
        outputPanel.add(splitPane, BorderLayout.CENTER);
        outputPanel.add(controlPanel, BorderLayout.SOUTH);
        
        panel.add(configPanel, BorderLayout.NORTH);
        panel.add(outputPanel, BorderLayout.CENTER);
        
        outputArea.append("API Security Checker loaded successfully!\n");
        outputArea.append("Configure domain filtering and start scanning...\n\n");
        
        return panel;
    }
    
    private JPanel createSensitiveInfoPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // Simple config panel
        JPanel configPanel = new JPanel(new FlowLayout());
        configPanel.add(new JLabel("Target URL:"));
        sensitiveUrlField = new JTextField(30);
        configPanel.add(sensitiveUrlField);
        
        sensitiveAnalyzeButton = new JButton("开始分析");
        sensitiveAnalyzeButton.addActionListener(e -> startSensitiveAnalysis());
        configPanel.add(sensitiveAnalyzeButton);
        
        autoAnalyzeCheckBox = new JCheckBox("自动分析JS文件", true);
        configPanel.add(autoAnalyzeCheckBox);
        
        panel.add(configPanel, BorderLayout.NORTH);
        
        // Simple output area for now
        JTextArea sensitiveOutputArea = new JTextArea();
        sensitiveOutputArea.setEditable(false);
        sensitiveOutputArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(sensitiveOutputArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        sensitiveOutputArea.append("敏感信息提取工具\n");
        sensitiveOutputArea.append("支持检测: API密钥、数据库信息、身份信息等\n\n");
        
        return panel;
    }
    
    private JPanel createDomainListPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("发现的域名"));
        
        // Domain count label
        domainCountLabel = new JLabel("域名数量: 0");
        domainCountLabel.setHorizontalAlignment(SwingConstants.CENTER);
        panel.add(domainCountLabel, BorderLayout.NORTH);
        
        // Domain list
        domainListModel = new DefaultListModel<>();
        domainList = new JList<>(domainListModel);
        domainList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // Double click to filter
        domainList.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    String selectedDomain = domainList.getSelectedValue();
                    if (selectedDomain != null) {
                        applyDomainFilter(selectedDomain);
                    }
                }
            }
        });
        
        JScrollPane domainScrollPane = new JScrollPane(domainList);
        panel.add(domainScrollPane, BorderLayout.CENTER);
        
        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton filterButton = new JButton("筛选");
        filterButton.addActionListener(e -> {
            String selectedDomain = domainList.getSelectedValue();
            if (selectedDomain != null) {
                applyDomainFilter(selectedDomain);
            }
        });
        buttonPanel.add(filterButton);
        
        JButton clearFilterButton = new JButton("清除筛选");
        clearFilterButton.addActionListener(e -> clearDomainFilter());
        buttonPanel.add(clearFilterButton);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    // Core functionality methods
    private void updateDomainFilters() {
        domainFilters.clear();
        String filterText = domainFilterField.getText().trim();
        if (!filterText.isEmpty()) {
            String[] filters = filterText.split(",");
            for (String filter : filters) {
                String trimmedFilter = filter.trim();
                if (!trimmedFilter.isEmpty()) {
                    domainFilters.add(trimmedFilter.toLowerCase());
                }
            }
        }
    }
    
    private boolean matchesDomainFilter(String url) {
        if (!enableDomainFilterCheckBox.isSelected() || domainFilters.isEmpty()) {
            return true;
        }
        
        try {
            URL urlObj = new URL(url);
            String host = urlObj.getHost().toLowerCase();
            
            for (String filter : domainFilters) {
                if (matchesDomainPattern(host, filter)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    private boolean matchesDomainPattern(String domain, String pattern) {
        String regex = pattern.replace(".", "\\.").replace("*", ".*");
        try {
            return domain.matches(regex);
        } catch (Exception e) {
            return domain.contains(pattern.replace("*", ""));
        }
    }
    
    private void addDiscoveredDomain(String url) {
        try {
            URL urlObj = new URL(url);
            String domain = urlObj.getHost();
            if (domain != null && !domain.isEmpty()) {
                boolean isNew = discoveredDomains.add(domain);
                if (isNew) {
                    SwingUtilities.invokeLater(() -> {
                        domainListModel.addElement(domain);
                        domainCountLabel.setText("域名数量: " + discoveredDomains.size());
                    });
                }
            }
        } catch (Exception e) {
            // Ignore URL parsing errors
        }
    }
    
    private void applyDomainFilter(String domain) {
        enableDomainFilterCheckBox.setSelected(true);
        domainFilterField.setEnabled(true);
        clearFilterButton.setEnabled(true);
        domainFilterField.setText(domain);
        updateDomainFilters();
        filterAndDisplayResults();
        outputArea.append("已应用域名过滤: " + domain + "\n");
    }
    
    private void clearDomainFilter() {
        enableDomainFilterCheckBox.setSelected(false);
        domainFilterField.setEnabled(false);
        clearFilterButton.setEnabled(false);
        domainFilterField.setText("");
        updateDomainFilters();
        filterAndDisplayResults();
        outputArea.append("已清除域名过滤\n");
    }
    
    private void filterAndDisplayResults() {
        SwingUtilities.invokeLater(() -> {
            StringBuilder filteredOutput = new StringBuilder();
            filteredOutput.append("=== 筛选结果 ===\n");
            
            if (enableDomainFilterCheckBox.isSelected() && !domainFilters.isEmpty()) {
                filteredOutput.append("过滤规则: ").append(String.join(", ", domainFilters)).append("\n");
            }
            
            // Filter JS files
            Set<String> filteredJsUrls = new HashSet<>();
            for (String jsUrl : jsUrls) {
                if (matchesDomainFilter(jsUrl)) {
                    filteredJsUrls.add(jsUrl);
                }
            }
            
            if (!filteredJsUrls.isEmpty()) {
                filteredOutput.append("\n筛选后的JavaScript文件 (").append(filteredJsUrls.size()).append("个):\n");
                for (String jsUrl : filteredJsUrls) {
                    filteredOutput.append("  - ").append(jsUrl).append("\n");
                }
            }
            
            // Filter API paths
            Set<String> filteredApiPaths = new HashSet<>();
            for (String apiPath : apiPaths) {
                String fullUrl = urlField.getText();
                if (fullUrl != null && !fullUrl.isEmpty()) {
                    try {
                        URL baseUrl = new URL(fullUrl);
                        String testUrl = baseUrl.getProtocol() + "://" + baseUrl.getHost() + apiPath;
                        if (matchesDomainFilter(testUrl)) {
                            filteredApiPaths.add(apiPath);
                        }
                    } catch (Exception e) {
                        filteredApiPaths.add(apiPath);
                    }
                }
            }
            
            if (!filteredApiPaths.isEmpty()) {
                filteredOutput.append("\n筛选后的API端点 (").append(filteredApiPaths.size()).append("个):\n");
                for (String apiPath : filteredApiPaths) {
                    filteredOutput.append("  - ").append(apiPath).append("\n");
                }
            }
            
            filteredOutput.append("\n统计: JS文件 ").append(filteredJsUrls.size())
                          .append("/").append(jsUrls.size())
                          .append(", API端点 ").append(filteredApiPaths.size())
                          .append("/").append(apiPaths.size()).append("\n");
            
            outputArea.setText(filteredOutput.toString());
        });
    }
    
    private void addScanResult(String type, String content, String sourceUrl) {
        allScanResults.add(new ScanResult(type, content, sourceUrl));
        
        if (enableDomainFilterCheckBox.isSelected() && !matchesDomainFilter(sourceUrl)) {
            return;
        }
        
        SwingUtilities.invokeLater(() -> {
            outputArea.append("[" + type + "] " + content + "\n");
        });
    }
    
    private void clearAllResults() {
        allScanResults.clear();
        apiPaths.clear();
        jsUrls.clear();
        discoveredDomains.clear();
        
        SwingUtilities.invokeLater(() -> {
            outputArea.setText("");
            domainListModel.clear();
            domainCountLabel.setText("域名数量: 0");
        });
    }
    
    private void findApiPaths(String content, String sourceUrl) {
        // Simple API path patterns
        String[] patterns = {
            "[\"\\']/api/[a-zA-Z0-9_/\\-]*",
            "[\"\\']/v\\d+/[a-zA-Z0-9_/\\-]*",
            "fetch\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
            "axios\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']"
        };
        
        Set<String> foundPaths = new HashSet<>();
        
        for (String patternStr : patterns) {
            try {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(content);
                
                while (matcher.find()) {
                    String path = matcher.group();
                    if (matcher.groupCount() > 0 && matcher.group(1) != null) {
                        path = matcher.group(1);
                    }
                    
                    path = path.replaceAll("[\"\']", "").trim();
                    
                    if (path.startsWith("/") && path.length() > 1) {
                        foundPaths.add(path);
                    }
                }
            } catch (Exception e) {
                // Ignore pattern errors
            }
        }
        
        apiPaths.addAll(foundPaths);
        
        if (!foundPaths.isEmpty()) {
            addScanResult("API Paths", "Found " + foundPaths.size() + " paths", sourceUrl);
            for (String path : foundPaths) {
                addScanResult("API", path, sourceUrl);
            }
        }
    }
    
    private void extractSensitiveInfo(String content, String sourceUrl) {
        for (String type : SENSITIVE_TYPES) {
            if (!sensitiveData.containsKey(type)) {
                sensitiveData.put(type, new ArrayList<>());
            }
        }
        
        for (int i = 0; i < SENSITIVE_PATTERNS.length && i < SENSITIVE_TYPES.length; i++) {
            Pattern pattern = SENSITIVE_PATTERNS[i];
            Matcher matcher = pattern.matcher(content);
            String typeName = SENSITIVE_TYPES[i];
            List<String> typeResults = sensitiveData.get(typeName);
            
            while (matcher.find()) {
                String match = matcher.group();
                if (match != null && !match.isEmpty() && !typeResults.contains(match)) {
                    typeResults.add(match);
                    sensitiveDataSources.put(match, sourceUrl);
                }
            }
        }
    }
    
    private void startScan() {
        String targetUrl = urlField.getText().trim();
        if (targetUrl.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "Please enter a target URL", "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        startButton.setEnabled(false);
        outputArea.append("Starting scan for: " + targetUrl + "\n");
        
        new Thread(() -> {
            try {
                performScan(targetUrl);
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    outputArea.append("Scan error: " + e.getMessage() + "\n");
                });
            } finally {
                SwingUtilities.invokeLater(() -> startButton.setEnabled(true));
            }
        }).start();
    }
    
    private void performScan(String targetUrl) {
        SwingUtilities.invokeLater(() -> outputArea.append("Analyzing target URL...\n"));
        
        try {
            URL url = new URL(targetUrl);
            IHttpService httpService = helpers.buildHttpService(url.getHost(), 
                url.getPort() == -1 ? (url.getProtocol().equals("https") ? 443 : 80) : url.getPort(), 
                url.getProtocol());
            
            byte[] request = helpers.buildHttpRequest(url);
            IHttpRequestResponse requestResponse = callbacks.makeHttpRequest(httpService, request);
            
            if (requestResponse.getResponse() != null) {
                String response = new String(requestResponse.getResponse());
                addDiscoveredDomain(targetUrl);
                
                // Find JavaScript files
                Set<String> foundJsUrls = findJavaScriptUrls(response, targetUrl);
                for (String jsUrl : foundJsUrls) {
                    jsUrls.add(jsUrl);
                    addDiscoveredDomain(jsUrl);
                    addScanResult("JS Found", jsUrl, jsUrl);
                }
                
                // Find API paths
                findApiPaths(response, targetUrl);
                
                // Extract sensitive info if enabled
                if (syncAnalyzeCheckBox.isSelected()) {
                    extractSensitiveInfo(response, targetUrl);
                }
                
                SwingUtilities.invokeLater(() -> {
                    outputArea.append("Scan completed!\n");
                    outputArea.append("Found " + jsUrls.size() + " JS files and " + apiPaths.size() + " API endpoints\n\n");
                });
            }
        } catch (Exception e) {
            SwingUtilities.invokeLater(() -> {
                outputArea.append("Error during scan: " + e.getMessage() + "\n");
            });
        }
    }
    
    private Set<String> findJavaScriptUrls(String html, String baseUrl) {
        Set<String> jsUrls = new HashSet<>();
        
        Pattern scriptPattern = Pattern.compile("<script[^>]+src=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher matcher = scriptPattern.matcher(html);
        
        while (matcher.find()) {
            String src = matcher.group(1);
            if (src.endsWith(".js")) {
                try {
                    if (src.startsWith("http")) {
                        jsUrls.add(src);
                    } else {
                        URL base = new URL(baseUrl);
                        URL jsUrl = new URL(base, src);
                        jsUrls.add(jsUrl.toString());
                    }
                } catch (Exception e) {
                    // Skip invalid URLs
                }
            }
        }
        
        return jsUrls;
    }
    
    private void startSensitiveAnalysis() {
        String url = sensitiveUrlField.getText().trim();
        if (url.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请输入目标URL", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        sensitiveAnalyzeButton.setEnabled(false);
        
        new Thread(() -> {
            try {
                // Simplified sensitive analysis
                outputArea.append("开始敏感信息分析: " + url + "\n");
                // Implementation would go here
            } finally {
                SwingUtilities.invokeLater(() -> sensitiveAnalyzeButton.setEnabled(true));
            }
        }).start();
    }
    
    private void exportResults() {
        StringBuilder sb = new StringBuilder();
        sb.append("API Security Checker Results\n");
        sb.append("============================\n\n");
        
        sb.append("扫描时间: ").append(new java.util.Date()).append("\n");
        sb.append("目标URL: ").append(urlField.getText()).append("\n");
        
        if (enableDomainFilterCheckBox.isSelected() && !domainFilters.isEmpty()) {
            sb.append("域名过滤: 已启用\n");
            sb.append("过滤规则: ").append(String.join(", ", domainFilters)).append("\n");
        } else {
            sb.append("域名过滤: 未启用\n");
        }
        sb.append("\n");
        
        sb.append("JavaScript Files Found:\n");
        for (String jsUrl : jsUrls) {
            sb.append("- ").append(jsUrl).append("\n");
        }
        
        sb.append("\nAPI Endpoints Found:\n");
        for (String apiPath : apiPaths) {
            sb.append("- ").append(apiPath).append("\n");
        }
        
        JTextArea textArea = new JTextArea(sb.toString());
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(600, 400));
        
        JOptionPane.showMessageDialog(mainPanel, scrollPane, "Export Results", JOptionPane.INFORMATION_MESSAGE);
    }
} 