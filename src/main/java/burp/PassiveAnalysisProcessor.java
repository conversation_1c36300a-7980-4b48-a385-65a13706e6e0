package burp;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 被动分析处理器
 * 统一处理所有被动分析逻辑
 */
public class PassiveAnalysisProcessor {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final PassiveAnalysisConfig config;
    private final List<IPassiveAnalysisReceiver> receivers;
    private final ExecutorService executorService;
    
    // API路径匹配模式 - 使用与ApiDiscoveryTab相同的完整规则
    private static final String[] API_PATTERN_STRINGS = {
        // === 基础API路径模式 (原有) ===
        "[\"\\']/[a-zA-Z0-9_/\\-]*api[a-zA-Z0-9_/\\-]*[\"\\'\\s]",
        "[\"\\']/[a-zA-Z0-9_/\\-]*[^a-zA-Z0-9_/\\-][a-zA-Z0-9_/\\-]*[\"\\'\\s]",
        "url\\s*[=:]\\s*[\"\\']/[a-zA-Z0-9_/\\-]+[\"\\'\\s]",
        "path\\s*[=:]\\s*[\"\\']/[a-zA-Z0-9_/\\-]+[\"\\'\\s]",
        "endpoint\\s*[=:]\\s*[\"\\']/[a-zA-Z0-9_/\\-]+[\"\\'\\s]",

        // === 增强的API路径模式 (参考Chrome扩展) ===
        // 1. 完整路径模式 - 匹配 '/path' 格式 (对应Chrome的path规则)
        "[\"\\''](?:/|\\.\\./|\\./)[^/\\>\\< \\)\\(\\{\\}\\,\\'\\\"\\\\]([^\\>\\< \\)\\(\\{\\}\\,\\'\\\"\\\\])*?[\"\\'']",

        // 2. 不完整路径模式 - 匹配 'path/subpath' 格式 (对应Chrome的incomplete_path规则)
        "[\"\\''][^/\\>\\< \\)\\(\\{\\}\\,\\'\\\"\\\\][\\w/]*?/[\\w/]*?[\"\\'']",

        // 3. HTTP请求方法相关模式
        "fetch\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
        "axios\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
        "\\$\\.(?:get|post|put|delete|ajax)\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
        "XMLHttpRequest.*?open\\s*\\([^,]*,\\s*[\"\\'']([^\"']+)[\"\\'']",

        // 4. 配置相关模式
        "baseURL\\s*[=:]\\s*[\"\\'']([^\"']+)[\"\\'']",
        "apiUrl\\s*[=:]\\s*[\"\\'']([^\"']+)[\"\\'']",
        "restUrl\\s*[=:]\\s*[\"\\'']([^\"']+)[\"\\'']",
        "serviceUrl\\s*[=:]\\s*[\"\\'']([^\"']+)[\"\\'']",

        // 5. 现代前端框架模式
        "this\\.[\\$]?http\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
        "http\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
        "httpClient\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",

        // 6. 特定关键词API模式
        "[\"\\']/[a-zA-Z0-9_/\\-]*(?:api|v\\d+|rest|service|ajax|json|gateway|admin|user|auth|login|register|upload|download)[a-zA-Z0-9_/\\-]*[\"\\'\\s]",

        // 7. 路由配置模式
        "(?i)(?<=path:)\\s?[\"\\'']([^\"']+)[\"\\'']",
        "(?i)(?<=path\\s:)\\s?[\"\\'']([^\"']+)[\"\\'']",
        "(?i)(?<=path=)\\s?[\"\\'']([^\"']+)[\"\\'']",
        "(?i)(?<=path\\s=)\\s?[\"\\'']([^\"']+)[\"\\'']",
        "(?i)(?<=url:)\\s?[\"\\'']([^\"']+)[\"\\'']",
        "(?i)(?<=url\\s:)\\s?[\"\\'']([^\"']+)[\"\\'']",
        "(?i)(?<=url=)\\s?[\"\\'']([^\"']+)[\"\\'']",
        "(?i)(?<=url\\s=)\\s?[\"\\'']([^\"']+)[\"\\'']",

        // 8. href和action属性
        "(href|action).{0,3}=.{0,3}[\"\\'']([^\\s\\'\"\\>\\<\\)\\(]{2,250})[\"\\'']",
        "(href|action).{0,3}=.{0,3}([^\\s\\'\"\\>\\<\\)\\(]{2,250})",

        // 9. 通用路径捕获 (包装在引号中的路径)
        "(?:\"|'|`)(/[^\"'`<>]+)(?:\"|'|`)",

        // 10. 排除汉字的路径模式
        "[\"\\']/[^\\s\\'\"\\>\\<\\:\\)\\(\\u4e00-\\u9fa5]{1,250}?[\"\\'']",
        "[\"\\''][^\\s\\'\"\\>\\<\\:\\)\\(\\u4e00-\\u9fa5]{1,250}?/[^\\s\\'\"\\>\\<\\:\\)\\(\\u4e00-\\u9fa5]{1,250}?/[^\\s\\'\"\\>\\<\\:\\)\\(\\u4e00-\\u9fa5]{1,250}?[\"\\'']",

        // 11. HTTP完整URL模式
        "[\"\\'']http[^\\s\\'\"\\>\\<\\)\\(]{2,250}?[\"\\'']",
        "=http[^\\s\\'\"\\>\\<\\)\\(]{2,250}"
    };
    
    // JavaScript文件匹配模式
    private static final Pattern[] JS_PATTERNS = {
        Pattern.compile("src\\s*=\\s*[\"\\'`]([^\"'`]*\\.js[^\"'`]*)[\"\\'`]", Pattern.CASE_INSENSITIVE),
        Pattern.compile("href\\s*=\\s*[\"\\'`]([^\"'`]*\\.js[^\"'`]*)[\"\\'`]", Pattern.CASE_INSENSITIVE),
        Pattern.compile("[\"\\'`](https?://[^\"'`]*\\.js[^\"'`]*)[\"\\'`]"),
        Pattern.compile("[\"\\'`](/[^\"'`]*\\.js[^\"'`]*)[\"\\'`]"),
        Pattern.compile("import\\s+.*from\\s+[\"\\'`]([^\"'`]*\\.js[^\"'`]*)[\"\\'`]"),
        Pattern.compile("require\\s*\\(\\s*[\"\\'`]([^\"'`]*\\.js[^\"'`]*)[\"\\'`]\\s*\\)")
    };
    
    // 使用统一的敏感信息配置 - 与其他组件保持一致
    
    public PassiveAnalysisProcessor(IBurpExtenderCallbacks callbacks, PassiveAnalysisConfig config) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        this.config = config;
        this.receivers = new CopyOnWriteArrayList<>();
        
        // 创建专用的线程池
        this.executorService = Executors.newFixedThreadPool(3, new ThreadFactory() {
            private int counter = 0;
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "PassiveAnalysis-" + (++counter));
                t.setDaemon(true);
                return t;
            }
        });
    }
    
    /**
     * 注册被动分析接收器
     */
    public void registerReceiver(IPassiveAnalysisReceiver receiver) {
        if (receiver != null && !receivers.contains(receiver)) {
            receivers.add(receiver);
            callbacks.printOutput("注册被动分析接收器: " + receiver.getReceiverName());
        }
    }
    
    /**
     * 取消注册被动分析接收器
     */
    public void unregisterReceiver(IPassiveAnalysisReceiver receiver) {
        if (receiver != null) {
            receivers.remove(receiver);
            callbacks.printOutput("取消注册被动分析接收器: " + receiver.getReceiverName());
        }
    }
    
    /**
     * 处理HTTP消息
     */
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        if (messageIsRequest || !config.isGlobalEnabled()) {
            return;
        }
        
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
            String url = requestInfo.getUrl().toString();
            
            // 检查是否应该处理此URL
            if (!config.shouldProcessUrl(url) || config.isUrlProcessed(url)) {
                return;
            }
            
            // 标记URL已处理
            config.markUrlProcessed(url);
            config.incrementTotalProcessed();
            
            // 获取响应内容
            byte[] response = messageInfo.getResponse();
            if (response == null || response.length == 0) {
                return;
            }
            
            // 检查响应大小
            if (response.length > config.getMaxResponseSize()) {
                callbacks.printOutput("跳过大文件分析: " + url + " (大小: " + response.length + " 字节)");
                return;
            }
            
            // 异步或同步处理
            if (config.isAsyncProcessing()) {
                executorService.submit(() -> analyzeResponse(url, response));
            } else {
                analyzeResponse(url, response);
            }
            
        } catch (Exception e) {
            callbacks.printError("被动分析处理错误: " + e.getMessage());
        }
    }
    
    /**
     * 分析响应内容
     */
    private void analyzeResponse(String url, byte[] response) {
        try {
            String responseString = new String(response);
            
            // 提取响应体（跳过HTTP头部）
            int bodyStart = responseString.indexOf("\r\n\r\n");
            if (bodyStart != -1) {
                responseString = responseString.substring(bodyStart + 4);
            }
            
            // 域名收集
            if (config.isDomainCollectionEnabled()) {
                collectDomain(url);
            }
            
            // API路径发现
            if (config.isApiDiscoveryEnabled()) {
                discoverApiPaths(responseString, url);
            }
            
            // JavaScript文件发现
            if (config.isJsAnalysisEnabled()) {
                discoverJavaScriptFiles(responseString, url);
            }
            
            // 敏感信息提取
            if (config.isSensitiveInfoEnabled()) {
                extractSensitiveInfo(responseString, url);
            }
            
        } catch (Exception e) {
            callbacks.printError("分析响应内容时出错: " + e.getMessage());
        }
    }
    
    /**
     * 收集域名
     */
    private void collectDomain(String url) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            String host = urlObj.getHost();
            int port = urlObj.getPort();
            
            String domain = host;
            if (port != -1 && port != 80 && port != 443) {
                domain = host + ":" + port;
            }
            
            // 通知所有接收器
            for (IPassiveAnalysisReceiver receiver : receivers) {
                if (receiver.isEnabled()) {
                    try {
                        receiver.onDomainDiscovered(domain, url);
                    } catch (Exception e) {
                        callbacks.printError("域名收集通知失败 [" + receiver.getReceiverName() + "]: " + e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            callbacks.printError("域名收集失败: " + e.getMessage());
        }
    }
    
    /**
     * 发现API路径 - 使用与ApiDiscoveryTab相同的完整规则
     */
    private void discoverApiPaths(String content, String sourceUrl) {
        Set<String> foundPaths = new HashSet<>();

        for (String patternStr : API_PATTERN_STRINGS) {
            try {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);
                Matcher matcher = pattern.matcher(content);

                while (matcher.find()) {
                    String path = null;

                    // 尝试从捕获组获取路径
                    if (matcher.groupCount() > 0 && matcher.group(1) != null) {
                        path = matcher.group(1);
                    } else if (matcher.groupCount() > 1 && matcher.group(2) != null) {
                        path = matcher.group(2);
                    } else {
                        // 从完整匹配中提取路径
                        String match = matcher.group();
                        path = extractPathFromMatch(match);
                    }

                    if (path != null) {
                        path = cleanApiPath(path);
                        if (isValidApiPath(path) && !foundPaths.contains(path)) {
                            foundPaths.add(path);
                            config.incrementApiPathsFound();

                            // 通知所有接收器
                            for (IPassiveAnalysisReceiver receiver : receivers) {
                                if (receiver.isEnabled()) {
                                    try {
                                        receiver.onApiPathDiscovered(path, sourceUrl, "GET"); // 默认GET方法
                                    } catch (Exception e) {
                                        callbacks.printError("API路径发现通知失败 [" + receiver.getReceiverName() + "]: " + e.getMessage());
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // 忽略正则表达式错误，继续处理下一个模式
                callbacks.printError("API路径模式错误: " + patternStr + " - " + e.getMessage());
            }
        }
    }
    
    /**
     * 发现JavaScript文件
     */
    private void discoverJavaScriptFiles(String content, String sourceUrl) {
        Set<String> foundJsFiles = new HashSet<>();
        
        for (Pattern pattern : JS_PATTERNS) {
            Matcher matcher = pattern.matcher(content);
            while (matcher.find()) {
                String jsUrl = matcher.group(1);
                jsUrl = resolveUrl(jsUrl, sourceUrl);
                
                if (jsUrl != null && !jsUrl.isEmpty() && !foundJsFiles.contains(jsUrl)) {
                    foundJsFiles.add(jsUrl);
                    config.incrementJsFilesFound();
                    
                    // 通知所有接收器
                    for (IPassiveAnalysisReceiver receiver : receivers) {
                        if (receiver.isEnabled()) {
                            try {
                                receiver.onJavaScriptFileDiscovered(jsUrl, sourceUrl);
                            } catch (Exception e) {
                                callbacks.printError("JS文件发现通知失败 [" + receiver.getReceiverName() + "]: " + e.getMessage());
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 提取敏感信息 - 使用统一的敏感信息配置
     */
    private void extractSensitiveInfo(String content, String sourceUrl) {
        // 使用统一的敏感信息规则
        for (SensitiveInfoConfig.SensitivePattern sensitivePattern : SensitiveInfoConfig.SENSITIVE_PATTERNS) {
            Matcher matcher = sensitivePattern.pattern.matcher(content);
            while (matcher.find()) {
                // 根据敏感信息类型选择合适的提取方式
                String match;
                if (shouldUseCaptureGroup(sensitivePattern.name) && matcher.groupCount() > 0 && matcher.group(1) != null) {
                    // 使用捕获组（去除包装字符）
                    match = matcher.group(1).trim();
                } else {
                    // 使用完整匹配
                    match = matcher.group().trim();
                }

                // 清理引号和其他包装字符
                match = cleanSensitiveValue(match);

                // 过滤明显的示例数据
                if (isExampleData(match, sensitivePattern.name)) {
                    continue;
                }

                if (match != null && !match.trim().isEmpty()) {
                    config.incrementSensitiveInfoFound();

                    // 获取上下文
                    String context = getContext(content, matcher.start(), matcher.end());

                    // 通知所有接收器
                    for (IPassiveAnalysisReceiver receiver : receivers) {
                        if (receiver.isEnabled()) {
                            try {
                                receiver.onSensitiveInfoDiscovered(sensitivePattern.name, match, sourceUrl, context);
                            } catch (Exception e) {
                                callbacks.printError("敏感信息发现通知失败 [" + receiver.getReceiverName() + "]: " + e.getMessage());
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 判断是否应该使用捕获组而不是完整匹配
     */
    private boolean shouldUseCaptureGroup(String patternName) {
        // 只有API路径需要使用捕获组去除包装的引号
        // 其他类型（包括认证Token、密码配置等）都保留完整匹配以便安全分析
        return "API路径".equals(patternName);
    }

    /**
     * 清理敏感信息值，移除包装的引号等字符
     */
    private String cleanSensitiveValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }

        String cleaned = value.trim();

        // 移除首尾的引号
        if ((cleaned.startsWith("\"") && cleaned.endsWith("\"")) ||
            (cleaned.startsWith("'") && cleaned.endsWith("'"))) {
            if (cleaned.length() > 2) {
                cleaned = cleaned.substring(1, cleaned.length() - 1);
            }
        }

        return cleaned.trim();
    }

    /**
     * 清理API路径
     */
    private String cleanApiPath(String path) {
        if (path == null) return null;
        
        // 移除引号
        path = path.replaceAll("[\"\\'`]", "").trim();
        
        // 移除末尾的空白字符
        path = path.replaceAll("\\s+$", "");
        
        // 确保以/开头
        if (!path.startsWith("/") && !path.startsWith("http")) {
            path = "/" + path;
        }
        
        return path;
    }
    
    /**
     * 解析相对URL为绝对URL
     */
    private String resolveUrl(String url, String baseUrl) {
        if (url == null || url.isEmpty()) return null;
        
        try {
            if (url.startsWith("http://") || url.startsWith("https://")) {
                return url;
            }
            
            java.net.URL base = new java.net.URL(baseUrl);
            java.net.URL resolved = new java.net.URL(base, url);
            return resolved.toString();
        } catch (Exception e) {
            return url;
        }
    }
    
    /**
     * 获取匹配内容的上下文
     */
    private String getContext(String content, int start, int end) {
        int contextStart = Math.max(0, start - 50);
        int contextEnd = Math.min(content.length(), end + 50);
        
        String context = content.substring(contextStart, contextEnd);
        
        // 高亮匹配部分
        String matched = content.substring(start, end);
        context = context.replace(matched, "**" + matched + "**");
        
        return context;
    }
    
    /**
     * 关闭处理器
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
    
    /**
     * 获取统计信息
     */
    public String getStatistics() {
        return String.format("被动分析统计: 总计%d, API路径%d, 敏感信息%d, JS文件%d, 接收器%d个",
                config.getTotalProcessed(),
                config.getApiPathsFound(),
                config.getSensitiveInfoFound(),
                config.getJsFilesFound(),
                receivers.size());
    }

    /**
     * 从匹配中提取路径 - 与ApiDiscoveryTab保持一致
     */
    private String extractPathFromMatch(String match) {
        if (match == null) return null;

        // 移除引号和空白字符
        match = match.replaceAll("[\"\\'`]", "").trim();

        // 查找路径部分
        if (match.contains("=")) {
            String[] parts = match.split("=", 2);
            if (parts.length > 1) {
                match = parts[1].trim();
            }
        }

        if (match.contains(":")) {
            String[] parts = match.split(":", 2);
            if (parts.length > 1) {
                match = parts[1].trim();
            }
        }

        return match;
    }

    /**
     * 验证API路径是否有效 - 与ApiDiscoveryTab保持一致
     */
    private boolean isValidApiPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return false;
        }

        path = path.trim();

        // 基本长度检查
        if (path.length() < 2 || path.length() > 500) {
            return false;
        }

        // 排除明显的静态资源
        String lowerPath = path.toLowerCase();
        String[] excludeExtensions = {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".ico", ".svg",
            ".css", ".js", ".woff", ".woff2", ".ttf", ".eot",
            ".mp4", ".avi", ".mov", ".wmv", ".flv",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".zip", ".rar", ".7z", ".tar", ".gz"
        };

        for (String ext : excludeExtensions) {
            if (lowerPath.endsWith(ext)) {
                return false;
            }
        }

        // 排除明显的非API路径
        if (lowerPath.contains("jquery") || lowerPath.contains("bootstrap") ||
            lowerPath.contains("angular") || lowerPath.contains("react") ||
            lowerPath.contains("vue") || lowerPath.contains("lodash") ||
            lowerPath.contains("moment") || lowerPath.contains("chart")) {
            return false;
        }

        // 必须包含有效字符
        if (!path.matches(".*[a-zA-Z0-9].*")) {
            return false;
        }

        return true;
    }

    /**
     * 过滤明显的示例数据 - 与其他组件保持一致
     */
    private boolean isExampleData(String match, String type) {
        if (match == null || match.trim().isEmpty()) {
            return true;
        }

        String lower = match.toLowerCase();

        // 通用示例数据过滤
        if (lower.contains("example") || lower.contains("test") ||
            lower.contains("demo") || lower.contains("sample") ||
            lower.contains("placeholder") || lower.contains("dummy")) {
            return true;
        }

        // 根据类型进行特殊处理
        switch (type) {
            case "邮箱地址":
                // 过滤示例邮箱
                if (match.contains("example.com") || match.contains("test.com") ||
                    match.contains("domain.com") || match.contains("localhost")) {
                    return true;
                }
                break;

            case "密码配置":
                // 过滤明显的示例密码
                if (lower.equals("password") || lower.equals("123456") ||
                    lower.length() < 6) {
                    return true;
                }
                break;

            case "IPv4地址":
                // 过滤明显的示例IP
                if (match.equals("127.0.0.1") || match.equals("0.0.0.0") ||
                    match.equals("***************") || match.startsWith("127.0.0.")) {
                    return true;
                }
                break;

            case "JWT Token":
                // JWT至少要包含两个点
                if (!match.contains(".") || match.split("\\.").length < 3) {
                    return true;
                }
                break;
        }

        return false;
    }
}
