package burp;

import java.util.*;
import java.time.LocalDateTime;

/**
 * 主机内容管理类 - 参考JsRouteScan的数据模型设计
 * 管理单个主机的所有相关信息
 */
public class HostContent {
    
    private String host;
    private List<RouteContent> routes;
    private List<EnhancedSensitiveInfoExtractor.SensitiveDataItem> sensitiveData;
    private Map<String, String> headers;
    private int totalRequests;
    private LocalDateTime lastScanTime;
    private boolean isActive;
    private String protocol;
    private int port;
    
    // 统计信息
    private int routeCount;
    private int sensitiveDataCount;
    private int jsFileCount;
    private int apiEndpointCount;
    
    public HostContent(String host) {
        this.host = host;
        this.routes = new ArrayList<>();
        this.sensitiveData = new ArrayList<>();
        this.headers = new HashMap<>();
        this.totalRequests = 0;
        this.lastScanTime = LocalDateTime.now();
        this.isActive = true;
        
        // 解析主机信息
        parseHostInfo();
    }
    
    /**
     * 解析主机信息，提取协议和端口
     */
    private void parseHostInfo() {
        try {
            if (host.contains("://")) {
                // 完整URL格式
                java.net.URL url = new java.net.URL(host.startsWith("http") ? host : "http://" + host);
                this.protocol = url.getProtocol();
                this.port = url.getPort();
                this.host = url.getHost() + (port != -1 && port != 80 && port != 443 ? ":" + port : "");
            } else if (host.contains(":")) {
                // 主机:端口格式
                String[] parts = host.split(":");
                this.host = parts[0];
                this.port = Integer.parseInt(parts[1]);
                this.protocol = (port == 443) ? "https" : "http";
            } else {
                // 纯主机名
                this.protocol = "http";
                this.port = 80;
            }
        } catch (Exception e) {
            this.protocol = "http";
            this.port = 80;
        }
    }
    
    /**
     * 添加路径信息
     */
    public void addRoute(RouteContent route) {
        if (!routes.contains(route)) {
            routes.add(route);
            routeCount++;
            
            // 判断是否为API端点
            if (isApiEndpoint(route.getPath())) {
                apiEndpointCount++;
            }
            
            updateLastScanTime();
        }
    }
    
    /**
     * 添加敏感信息
     */
    public void addSensitiveData(EnhancedSensitiveInfoExtractor.SensitiveDataItem item) {
        if (!sensitiveData.contains(item)) {
            sensitiveData.add(item);
            sensitiveDataCount++;
            updateLastScanTime();
        }
    }
    
    /**
     * 判断是否为API端点
     */
    private boolean isApiEndpoint(String path) {
        String lowerPath = path.toLowerCase();
        return lowerPath.contains("/api/") || 
               lowerPath.contains("/v1/") || 
               lowerPath.contains("/v2/") ||
               lowerPath.contains("/rest/") ||
               lowerPath.contains("/graphql") ||
               lowerPath.endsWith(".json") ||
               lowerPath.endsWith(".xml");
    }
    
    /**
     * 更新最后扫描时间
     */
    private void updateLastScanTime() {
        this.lastScanTime = LocalDateTime.now();
    }
    
    /**
     * 增加请求计数
     */
    public void incrementRequestCount() {
        this.totalRequests++;
        updateLastScanTime();
    }
    
    /**
     * 添加HTTP头信息
     */
    public void addHeader(String name, String value) {
        headers.put(name, value);
    }
    
    /**
     * 获取统计摘要
     */
    public String getStatsSummary() {
        return String.format("路径: %d, 敏感信息: %d, API: %d, JS: %d", 
            routeCount, sensitiveDataCount, apiEndpointCount, jsFileCount);
    }
    
    /**
     * 获取风险等级
     */
    public String getRiskLevel() {
        if (sensitiveDataCount > 10) {
            return "高";
        } else if (sensitiveDataCount > 5) {
            return "中";
        } else if (sensitiveDataCount > 0) {
            return "低";
        } else {
            return "无";
        }
    }
    
    /**
     * 获取完整URL
     */
    public String getFullUrl() {
        return protocol + "://" + host;
    }
    
    // Getter和Setter方法
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public List<RouteContent> getRoutes() {
        return routes;
    }
    
    public List<EnhancedSensitiveInfoExtractor.SensitiveDataItem> getSensitiveData() {
        return sensitiveData;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public int getTotalRequests() {
        return totalRequests;
    }
    
    public void setTotalRequests(int totalRequests) {
        this.totalRequests = totalRequests;
    }
    
    public LocalDateTime getLastScanTime() {
        return lastScanTime;
    }
    
    public void setLastScanTime(LocalDateTime lastScanTime) {
        this.lastScanTime = lastScanTime;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public int getRouteCount() {
        return routeCount;
    }
    
    public int getSensitiveDataCount() {
        return sensitiveDataCount;
    }
    
    public int getJsFileCount() {
        return jsFileCount;
    }
    
    public void setJsFileCount(int jsFileCount) {
        this.jsFileCount = jsFileCount;
    }
    
    public int getApiEndpointCount() {
        return apiEndpointCount;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        HostContent that = (HostContent) obj;
        return Objects.equals(host, that.host);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(host);
    }
    
    @Override
    public String toString() {
        return String.format("HostContent{host='%s', routes=%d, sensitiveData=%d, active=%s}", 
            host, routeCount, sensitiveDataCount, isActive);
    }
}
