package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;

/**
 * API测试结果Tab - 专门显示和管理API测试结果
 * 参考JsRouteScan项目的scan页功能设计
 */
public class ApiTestResultTab {
    
    private IBurpExtenderCallbacks callbacks;
    private ApiScanMainTab mainTab;
    
    // UI组件
    private JPanel mainPanel;
    private JSplitPane splitPane;
    private JTable resultTable;
    private DefaultTableModel tableModel;
    private TableRowSorter<DefaultTableModel> sorter;
    private JTextArea detailArea;
    private JTextArea requestDetailArea;
    private JTextArea responseDetailArea;
    private JLabel statusLabel;

    // Burp原生HTTP查看器组件
    private IMessageEditor requestViewer;
    private IMessageEditor responseViewer;
    
    // 过滤和搜索组件
    private JComboBox<String> statusFilterCombo;
    private JComboBox<String> methodFilterCombo;
    private JComboBox<String> hostFilterCombo;
    private JTextField searchField;
    private JButton clearSearchButton;
    
    // 数据
    private List<ApiTestResult> testResults;
    private Set<String> discoveredHosts;
    
    // 表格列定义
    private static final String[] COLUMN_NAMES = {
        "选择", "URL", "方法", "状态码", "响应时间(ms)", "内容长度", "测试时间", "结果", "主机"
    };
    
    public ApiTestResultTab(IBurpExtenderCallbacks callbacks, ApiScanMainTab mainTab) {
        this.callbacks = callbacks;
        this.mainTab = mainTab;
        this.testResults = new ArrayList<>();
        this.discoveredHosts = new HashSet<>();
        
        initializeUI();
    }
    
    /**
     * 初始化UI
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建工具栏
        JPanel toolbar = createToolbar();
        mainPanel.add(toolbar, BorderLayout.NORTH);
        
        // 创建分割面板
        splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        splitPane.setResizeWeight(0.7);
        
        // 创建结果表格
        createResultTable();
        JScrollPane tableScrollPane = new JScrollPane(resultTable);
        splitPane.setTopComponent(tableScrollPane);
        
        // 创建详情面板
        createDetailPanel();
        splitPane.setBottomComponent(createDetailPanel());
        
        mainPanel.add(splitPane, BorderLayout.CENTER);
        
        // 创建状态栏
        statusLabel = new JLabel("测试结果: 0条");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        mainPanel.add(statusLabel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建工具栏
     */
    private JPanel createToolbar() {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        toolbar.setBorder(BorderFactory.createEtchedBorder());
        
        // 搜索功能
        toolbar.add(new JLabel("搜索:"));
        searchField = new JTextField(15);
        searchField.setToolTipText("搜索URL、状态码或错误信息");
        searchField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            public void insertUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
            public void removeUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
            public void changedUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
        });
        toolbar.add(searchField);
        
        clearSearchButton = new JButton("×");
        clearSearchButton.setToolTipText("清除搜索");
        clearSearchButton.addActionListener(e -> clearSearch());
        toolbar.add(clearSearchButton);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 状态过滤
        toolbar.add(new JLabel("状态:"));
        statusFilterCombo = new JComboBox<>(new String[]{
            "全部状态", "成功(2xx)", "重定向(3xx)", "客户端错误(4xx)", "服务器错误(5xx)", "连接失败"
        });
        statusFilterCombo.addActionListener(e -> applyFilters());
        toolbar.add(statusFilterCombo);
        
        // 方法过滤
        toolbar.add(new JLabel("方法:"));
        methodFilterCombo = new JComboBox<>();
        methodFilterCombo.addItem("全部方法");
        methodFilterCombo.addActionListener(e -> applyFilters());
        toolbar.add(methodFilterCombo);
        
        // 主机过滤
        toolbar.add(new JLabel("主机:"));
        hostFilterCombo = new JComboBox<>();
        hostFilterCombo.addItem("全部主机");
        hostFilterCombo.addActionListener(e -> applyFilters());
        toolbar.add(hostFilterCombo);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 操作按钮
        JButton refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> refreshData());
        toolbar.add(refreshButton);
        
        JButton clearButton = new JButton("清除");
        clearButton.addActionListener(e -> clearResults());
        toolbar.add(clearButton);
        
        JButton exportButton = new JButton("导出");
        exportButton.addActionListener(e -> exportResults());
        toolbar.add(exportButton);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        JButton selectAllButton = new JButton("全选");
        selectAllButton.addActionListener(e -> selectAllResults(true));
        toolbar.add(selectAllButton);
        
        JButton deselectAllButton = new JButton("取消全选");
        deselectAllButton.addActionListener(e -> selectAllResults(false));
        toolbar.add(deselectAllButton);
        
        return toolbar;
    }
    
    /**
     * 创建结果表格
     */
    private void createResultTable() {
        tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 0) return Boolean.class; // 选择列
                return String.class;
            }
            
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0; // 只有选择列可编辑
            }
        };
        
        resultTable = new JTable(tableModel);
        resultTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultTable.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
        
        // 设置列宽
        resultTable.getColumnModel().getColumn(0).setPreferredWidth(50);  // 选择
        resultTable.getColumnModel().getColumn(1).setPreferredWidth(300); // URL
        resultTable.getColumnModel().getColumn(2).setPreferredWidth(60);  // 方法
        resultTable.getColumnModel().getColumn(3).setPreferredWidth(80);  // 状态码
        resultTable.getColumnModel().getColumn(4).setPreferredWidth(100); // 响应时间
        resultTable.getColumnModel().getColumn(5).setPreferredWidth(80);  // 内容长度
        resultTable.getColumnModel().getColumn(6).setPreferredWidth(120); // 测试时间
        resultTable.getColumnModel().getColumn(7).setPreferredWidth(80);  // 结果
        resultTable.getColumnModel().getColumn(8).setPreferredWidth(150); // 主机
        
        // 添加行选择监听器
        resultTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showResultDetail();
            }
        });
        
        // 添加右键菜单
        createContextMenu();
    }
    
    /**
     * 创建详情面板
     */
    private JPanel createDetailPanel() {
        JPanel detailPanel = new JPanel(new BorderLayout());
        detailPanel.setBorder(BorderFactory.createTitledBorder("测试详情"));

        // 创建Tab面板用于显示不同类型的详情
        JTabbedPane detailTabs = new JTabbedPane();

        // 基本信息Tab
        detailArea = new JTextArea();
        detailArea.setEditable(false);
        detailArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailArea.setText("选择一个测试结果查看详细信息...");
        JScrollPane basicScrollPane = new JScrollPane(detailArea);
        detailTabs.addTab("基本信息", basicScrollPane);

        // 创建Burp原生HTTP查看器组件
        try {
            // 创建消息编辑器控制器
            IMessageEditorController controller = new IMessageEditorController() {
                @Override
                public IHttpService getHttpService() {
                    return null; // 可以返回null，因为我们只是查看消息
                }

                @Override
                public byte[] getRequest() {
                    return null;
                }

                @Override
                public byte[] getResponse() {
                    return null;
                }
            };

            // 创建请求查看器
            requestViewer = callbacks.createMessageEditor(controller, false);
            detailTabs.addTab("请求详情", requestViewer.getComponent());

            // 创建响应查看器
            responseViewer = callbacks.createMessageEditor(controller, false);
            detailTabs.addTab("📥 响应详情", responseViewer.getComponent());

        } catch (Exception e) {
            // 如果Burp原生组件创建失败，回退到文本显示
            callbacks.printError("无法创建Burp原生HTTP查看器，回退到文本显示: " + e.getMessage());

            // 请求详情Tab (文本模式)
            requestDetailArea = new JTextArea();
            requestDetailArea.setEditable(false);
            requestDetailArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            requestDetailArea.setText("选择一个测试结果查看请求详情...");
            JScrollPane requestScrollPane = new JScrollPane(requestDetailArea);
            detailTabs.addTab("请求详情", requestScrollPane);

            // 响应详情Tab (文本模式)
            responseDetailArea = new JTextArea();
            responseDetailArea.setEditable(false);
            responseDetailArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            responseDetailArea.setText("选择一个测试结果查看响应详情...");
            JScrollPane responseScrollPane = new JScrollPane(responseDetailArea);
            detailTabs.addTab("📥 响应详情", responseScrollPane);
        }

        detailPanel.add(detailTabs, BorderLayout.CENTER);

        return detailPanel;
    }
    
    /**
     * 创建右键菜单
     */
    private void createContextMenu() {
        JPopupMenu contextMenu = new JPopupMenu();
        
        JMenuItem copyUrlItem = new JMenuItem("复制URL");
        copyUrlItem.addActionListener(e -> copySelectedUrl());
        contextMenu.add(copyUrlItem);
        
        JMenuItem sendToRepeaterItem = new JMenuItem("发送到Repeater");
        sendToRepeaterItem.addActionListener(e -> sendToRepeater());
        contextMenu.add(sendToRepeaterItem);
        
        contextMenu.addSeparator();
        
        JMenuItem retestItem = new JMenuItem("重新测试");
        retestItem.addActionListener(e -> retestSelected());
        contextMenu.add(retestItem);
        
        JMenuItem removeItem = new JMenuItem("移除");
        removeItem.addActionListener(e -> removeSelected());
        contextMenu.add(removeItem);
        
        resultTable.setComponentPopupMenu(contextMenu);
    }
    
    /**
     * API测试结果数据类
     */
    public static class ApiTestResult {
        private String url;
        private String method;
        private int statusCode;
        private long responseTime;
        private int contentLength;
        private LocalDateTime testTime;
        private boolean success;
        private String errorMessage;
        private String responseHeaders;
        private String responseBody;
        private String host;

        // 新增：完整的请求和响应信息
        private byte[] requestBytes;
        private byte[] responseBytes;
        private String requestHeaders;
        private String requestBody;
        private String statusMessage;
        
        public ApiTestResult(String url, String method) {
            this.url = url;
            this.method = method;
            this.testTime = LocalDateTime.now();
            this.host = extractHost(url);
        }
        
        private String extractHost(String url) {
            try {
                if (url.startsWith("http://") || url.startsWith("https://")) {
                    java.net.URL urlObj = new java.net.URL(url);
                    String host = urlObj.getHost();
                    int port = urlObj.getPort();
                    if (port != -1 && port != 80 && port != 443) {
                        return host + ":" + port;
                    }
                    return host;
                }
                return "本地";
            } catch (Exception e) {
                return "未知主机";
            }
        }
        
        // Getter和Setter方法
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        
        public int getStatusCode() { return statusCode; }
        public void setStatusCode(int statusCode) { this.statusCode = statusCode; }
        
        public long getResponseTime() { return responseTime; }
        public void setResponseTime(long responseTime) { this.responseTime = responseTime; }
        
        public int getContentLength() { return contentLength; }
        public void setContentLength(int contentLength) { this.contentLength = contentLength; }
        
        public LocalDateTime getTestTime() { return testTime; }
        public void setTestTime(LocalDateTime testTime) { this.testTime = testTime; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public String getResponseHeaders() { return responseHeaders; }
        public void setResponseHeaders(String responseHeaders) { this.responseHeaders = responseHeaders; }
        
        public String getResponseBody() { return responseBody; }
        public void setResponseBody(String responseBody) { this.responseBody = responseBody; }
        
        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }

        // 新增字段的getter和setter方法
        public byte[] getRequestBytes() { return requestBytes; }
        public void setRequestBytes(byte[] requestBytes) { this.requestBytes = requestBytes; }

        public byte[] getResponseBytes() { return responseBytes; }
        public void setResponseBytes(byte[] responseBytes) { this.responseBytes = responseBytes; }

        public String getRequestHeaders() { return requestHeaders; }
        public void setRequestHeaders(String requestHeaders) { this.requestHeaders = requestHeaders; }

        public String getRequestBody() { return requestBody; }
        public void setRequestBody(String requestBody) { this.requestBody = requestBody; }

        public String getStatusMessage() { return statusMessage; }
        public void setStatusMessage(String statusMessage) { this.statusMessage = statusMessage; }
    }
    
    /**
     * 添加测试结果
     */
    public void addTestResult(ApiTestResult result) {
        testResults.add(result);
        discoveredHosts.add(result.getHost());
        updateTableRow(result);
        updateFilters();
        updateStatusLabel();
    }

    /**
     * 更新表格行
     */
    private void updateTableRow(ApiTestResult result) {
        SwingUtilities.invokeLater(() -> {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");
            String testTime = result.getTestTime().format(formatter);

            String statusText = getStatusText(result);
            String resultText = result.isSuccess() ? "✅ 成功" : "❌ 失败";

            Object[] rowData = {
                false, // 选择状态
                result.getUrl(),
                result.getMethod(),
                result.getStatusCode() > 0 ? String.valueOf(result.getStatusCode()) : "N/A",
                result.getResponseTime() + "ms",
                result.getContentLength() > 0 ? String.valueOf(result.getContentLength()) : "N/A",
                testTime,
                resultText,
                result.getHost()
            };

            tableModel.addRow(rowData);
        });
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(ApiTestResult result) {
        int statusCode = result.getStatusCode();
        if (statusCode == 0) {
            return "连接失败";
        } else if (statusCode >= 200 && statusCode < 300) {
            return "成功";
        } else if (statusCode >= 300 && statusCode < 400) {
            return "重定向";
        } else if (statusCode >= 400 && statusCode < 500) {
            return "客户端错误";
        } else if (statusCode >= 500) {
            return "服务器错误";
        }
        return "未知";
    }

    /**
     * 显示结果详情
     */
    private void showResultDetail() {
        int selectedRow = resultTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = resultTable.convertRowIndexToModel(selectedRow);
            String url = (String) tableModel.getValueAt(modelRow, 1);

            // 查找对应的测试结果
            ApiTestResult result = testResults.stream()
                .filter(r -> r.getUrl().equals(url))
                .findFirst()
                .orElse(null);

            if (result != null) {
                // 更新基本信息Tab
                updateBasicInfo(result);

                // 使用Burp原生HTTP查看器显示请求和响应
                if (requestViewer != null && responseViewer != null) {
                    updateBurpViewers(result);
                } else {
                    // 回退到文本模式
                    updateRequestDetail(result);
                    updateResponseDetail(result);
                }
            }
        } else {
            clearDetailAreas();
        }
    }

    /**
     * 使用Burp原生HTTP查看器更新请求和响应显示
     */
    private void updateBurpViewers(ApiTestResult result) {
        try {
            // 更新请求查看器
            if (result.getRequestBytes() != null && result.getRequestBytes().length > 0) {
                requestViewer.setMessage(result.getRequestBytes(), true);
            } else {
                // 如果没有完整的请求字节数据，构建一个
                String requestString = buildHttpRequestString(result);
                requestViewer.setMessage(requestString.getBytes(), true);
            }

            // 更新响应查看器
            if (result.getResponseBytes() != null && result.getResponseBytes().length > 0) {
                responseViewer.setMessage(result.getResponseBytes(), false);
            } else {
                // 如果没有完整的响应字节数据，构建一个
                String responseString = buildHttpResponseString(result);
                responseViewer.setMessage(responseString.getBytes(), false);
            }

        } catch (Exception e) {
            callbacks.printError("更新Burp HTTP查看器失败: " + e.getMessage());
            // 回退到文本模式
            updateRequestDetail(result);
            updateResponseDetail(result);
        }
    }

    /**
     * 构建HTTP请求字符串
     */
    private String buildHttpRequestString(ApiTestResult result) {
        StringBuilder request = new StringBuilder();

        try {
            java.net.URL urlObj = new java.net.URL(result.getUrl());
            String path = urlObj.getPath();
            if (urlObj.getQuery() != null) {
                path += "?" + urlObj.getQuery();
            }

            // 请求行
            request.append(result.getMethod()).append(" ").append(path).append(" HTTP/1.1\r\n");

            // 请求头
            if (result.getRequestHeaders() != null && !result.getRequestHeaders().isEmpty()) {
                request.append(result.getRequestHeaders().replace("\n", "\r\n"));
            } else {
                request.append("Host: ").append(urlObj.getHost()).append("\r\n");
                request.append("User-Agent: Burp API Checker\r\n");
                request.append("Accept: application/json, */*\r\n");
                request.append("Connection: close\r\n");
            }

            request.append("\r\n");

            // 请求体
            if (result.getRequestBody() != null && !result.getRequestBody().isEmpty()) {
                request.append(result.getRequestBody());
            }

        } catch (Exception e) {
            request.append(result.getMethod()).append(" ").append(result.getUrl()).append(" HTTP/1.1\r\n");
            request.append("User-Agent: Burp API Checker\r\n");
            request.append("Accept: */*\r\n");
            request.append("Connection: close\r\n");
            request.append("\r\n");
        }

        return request.toString();
    }

    /**
     * 构建HTTP响应字符串
     */
    private String buildHttpResponseString(ApiTestResult result) {
        StringBuilder response = new StringBuilder();

        // 响应状态行
        response.append("HTTP/1.1 ").append(result.getStatusCode());
        if (result.getStatusMessage() != null && !result.getStatusMessage().isEmpty()) {
            response.append(" ").append(result.getStatusMessage());
        }
        response.append("\r\n");

        // 响应头
        if (result.getResponseHeaders() != null && !result.getResponseHeaders().isEmpty()) {
            response.append(result.getResponseHeaders().replace("\n", "\r\n"));
        } else {
            response.append("Content-Type: application/json\r\n");
            response.append("Content-Length: ").append(result.getContentLength()).append("\r\n");
            response.append("Server: Mock Server\r\n");
        }

        response.append("\r\n");

        // 响应体
        if (result.getResponseBody() != null && !result.getResponseBody().isEmpty()) {
            response.append(result.getResponseBody());
        }

        return response.toString();
    }

    /**
     * 更新基本信息
     */
    private void updateBasicInfo(ApiTestResult result) {
        StringBuilder detail = new StringBuilder();
        detail.append("=== API测试基本信息 ===\n\n");
        detail.append("URL: ").append(result.getUrl()).append("\n");
        detail.append("方法: ").append(result.getMethod()).append("\n");
        detail.append("主机: ").append(result.getHost()).append("\n");
        detail.append("状态码: ").append(result.getStatusCode());
        if (result.getStatusMessage() != null && !result.getStatusMessage().isEmpty()) {
            detail.append(" ").append(result.getStatusMessage());
        }
        detail.append("\n");
        detail.append("响应时间: ").append(result.getResponseTime()).append("ms\n");
        detail.append("内容长度: ").append(result.getContentLength()).append(" bytes\n");
        detail.append("测试时间: ").append(result.getTestTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        detail.append("测试结果: ").append(result.isSuccess() ? "✅ 成功" : "❌ 失败").append("\n");

        if (result.getErrorMessage() != null && !result.getErrorMessage().isEmpty()) {
            detail.append("\n=== 错误信息 ===\n");
            detail.append(result.getErrorMessage()).append("\n");
        }

        detailArea.setText(detail.toString());
        detailArea.setCaretPosition(0);
    }

    /**
     * 更新请求详情
     */
    private void updateRequestDetail(ApiTestResult result) {
        StringBuilder requestDetail = new StringBuilder();
        requestDetail.append("=== HTTP请求详情 ===\n\n");

        // 请求行
        requestDetail.append(result.getMethod()).append(" ");
        try {
            java.net.URL urlObj = new java.net.URL(result.getUrl());
            String path = urlObj.getPath();
            if (urlObj.getQuery() != null) {
                path += "?" + urlObj.getQuery();
            }
            requestDetail.append(path).append(" HTTP/1.1\n");
            requestDetail.append("Host: ").append(urlObj.getHost());
            if (urlObj.getPort() != -1 && urlObj.getPort() != 80 && urlObj.getPort() != 443) {
                requestDetail.append(":").append(urlObj.getPort());
            }
            requestDetail.append("\n");
        } catch (Exception e) {
            requestDetail.append(result.getUrl()).append(" HTTP/1.1\n");
        }

        // 请求头
        if (result.getRequestHeaders() != null && !result.getRequestHeaders().isEmpty()) {
            requestDetail.append(result.getRequestHeaders());
        } else {
            requestDetail.append("User-Agent: Burp API Checker\n");
            requestDetail.append("Accept: */*\n");
            requestDetail.append("Connection: close\n");
        }

        requestDetail.append("\n");

        // 请求体
        if (result.getRequestBody() != null && !result.getRequestBody().isEmpty()) {
            requestDetail.append("=== 请求体 ===\n");
            requestDetail.append(result.getRequestBody());
        } else if ("POST".equals(result.getMethod()) || "PUT".equals(result.getMethod()) || "PATCH".equals(result.getMethod())) {
            requestDetail.append("=== 请求体 ===\n");
            requestDetail.append("(无请求体)");
        }

        requestDetailArea.setText(requestDetail.toString());
        requestDetailArea.setCaretPosition(0);
    }

    /**
     * 更新响应详情
     */
    private void updateResponseDetail(ApiTestResult result) {
        StringBuilder responseDetail = new StringBuilder();
        responseDetail.append("=== HTTP响应详情 ===\n\n");

        // 响应状态行
        responseDetail.append("HTTP/1.1 ").append(result.getStatusCode());
        if (result.getStatusMessage() != null && !result.getStatusMessage().isEmpty()) {
            responseDetail.append(" ").append(result.getStatusMessage());
        }
        responseDetail.append("\n");

        // 响应头
        if (result.getResponseHeaders() != null && !result.getResponseHeaders().isEmpty()) {
            responseDetail.append(result.getResponseHeaders()).append("\n");
        } else {
            responseDetail.append("Content-Length: ").append(result.getContentLength()).append("\n");
            responseDetail.append("Content-Type: application/json\n");
            responseDetail.append("\n");
        }

        // 响应体
        if (result.getResponseBody() != null && !result.getResponseBody().isEmpty()) {
            responseDetail.append("=== 响应体 ===\n");
            String body = result.getResponseBody();
            if (body.length() > 5000) {
                responseDetail.append(body.substring(0, 5000));
                responseDetail.append("\n\n... (响应体过长，已截断，完整内容共 ");
                responseDetail.append(body.length()).append(" 字符)");
            } else {
                responseDetail.append(body);
            }
        } else if (result.getStatusCode() > 0) {
            responseDetail.append("=== 响应体 ===\n");
            responseDetail.append("(无响应体或响应体为空)");
        }

        responseDetailArea.setText(responseDetail.toString());
        responseDetailArea.setCaretPosition(0);
    }

    /**
     * 清除详情显示区域
     */
    private void clearDetailAreas() {
        detailArea.setText("选择一个测试结果查看详细信息...");

        // 清除Burp原生查看器
        if (requestViewer != null) {
            requestViewer.setMessage(new byte[0], true);
        }
        if (responseViewer != null) {
            responseViewer.setMessage(new byte[0], false);
        }

        // 清除文本区域（如果存在）
        if (requestDetailArea != null) {
            requestDetailArea.setText("选择一个测试结果查看请求详情...");
        }
        if (responseDetailArea != null) {
            responseDetailArea.setText("选择一个测试结果查看响应详情...");
        }
    }

    /**
     * 更新过滤器选项
     */
    private void updateFilters() {
        SwingUtilities.invokeLater(() -> {
            // 更新方法过滤器
            String currentMethod = (String) methodFilterCombo.getSelectedItem();
            methodFilterCombo.removeAllItems();
            methodFilterCombo.addItem("全部方法");

            testResults.stream()
                .map(ApiTestResult::getMethod)
                .distinct()
                .sorted()
                .forEach(methodFilterCombo::addItem);

            if (currentMethod != null) {
                methodFilterCombo.setSelectedItem(currentMethod);
            }

            // 更新主机过滤器
            String currentHost = (String) hostFilterCombo.getSelectedItem();
            hostFilterCombo.removeAllItems();
            hostFilterCombo.addItem("全部主机");

            discoveredHosts.stream()
                .sorted()
                .forEach(hostFilterCombo::addItem);

            if (currentHost != null) {
                hostFilterCombo.setSelectedItem(currentHost);
            }
        });
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        applyFilters();
    }

    /**
     * 清除搜索
     */
    private void clearSearch() {
        searchField.setText("");
        applyFilters();
    }

    /**
     * 应用过滤器
     */
    private void applyFilters() {
        String searchText = searchField.getText().trim().toLowerCase();
        String selectedStatus = (String) statusFilterCombo.getSelectedItem();
        String selectedMethod = (String) methodFilterCombo.getSelectedItem();
        String selectedHost = (String) hostFilterCombo.getSelectedItem();

        if (searchText.isEmpty() && "全部状态".equals(selectedStatus) &&
            "全部方法".equals(selectedMethod) && "全部主机".equals(selectedHost)) {
            resultTable.setRowSorter(null);
            updateStatusLabel();
            return;
        }

        RowFilter<DefaultTableModel, Object> filter = new RowFilter<DefaultTableModel, Object>() {
            @Override
            public boolean include(Entry<? extends DefaultTableModel, ? extends Object> entry) {
                // 搜索过滤
                if (!searchText.isEmpty()) {
                    String url = entry.getStringValue(1).toLowerCase();
                    String statusCode = entry.getStringValue(3).toLowerCase();
                    String result = entry.getStringValue(7).toLowerCase();

                    if (!url.contains(searchText) && !statusCode.contains(searchText) && !result.contains(searchText)) {
                        return false;
                    }
                }

                // 状态过滤
                if (!"全部状态".equals(selectedStatus)) {
                    String statusCode = entry.getStringValue(3);
                    if (!matchesStatusFilter(statusCode, selectedStatus)) {
                        return false;
                    }
                }

                // 方法过滤
                if (!"全部方法".equals(selectedMethod)) {
                    if (!selectedMethod.equals(entry.getStringValue(2))) {
                        return false;
                    }
                }

                // 主机过滤
                if (!"全部主机".equals(selectedHost)) {
                    if (!selectedHost.equals(entry.getStringValue(8))) {
                        return false;
                    }
                }

                return true;
            }
        };

        if (sorter == null) {
            sorter = new TableRowSorter<>(tableModel);
        }
        sorter.setRowFilter(filter);
        resultTable.setRowSorter(sorter);

        updateStatusLabel();
    }

    /**
     * 检查状态码是否匹配过滤器
     */
    private boolean matchesStatusFilter(String statusCode, String filter) {
        if ("N/A".equals(statusCode)) {
            return "连接失败".equals(filter);
        }

        try {
            int code = Integer.parseInt(statusCode);
            switch (filter) {
                case "成功(2xx)":
                    return code >= 200 && code < 300;
                case "重定向(3xx)":
                    return code >= 300 && code < 400;
                case "客户端错误(4xx)":
                    return code >= 400 && code < 500;
                case "服务器错误(5xx)":
                    return code >= 500;
                case "连接失败":
                    return false;
                default:
                    return true;
            }
        } catch (NumberFormatException e) {
            return "连接失败".equals(filter);
        }
    }

    /**
     * 更新状态标签
     */
    private void updateStatusLabel() {
        SwingUtilities.invokeLater(() -> {
            int totalResults = testResults.size();
            int visibleResults = resultTable.getRowCount();

            if (totalResults == visibleResults) {
                statusLabel.setText("测试结果: " + totalResults + "条");
            } else {
                statusLabel.setText("测试结果: " + visibleResults + "/" + totalResults + "条 (已过滤)");
            }
        });
    }

    /**
     * 刷新数据
     */
    private void refreshData() {
        updateFilters();
        updateStatusLabel();
    }

    /**
     * 清除结果
     */
    private void clearResults() {
        int result = JOptionPane.showConfirmDialog(
            mainPanel,
            "确定要清除所有测试结果吗？",
            "确认清除",
            JOptionPane.YES_NO_OPTION
        );

        if (result == JOptionPane.YES_OPTION) {
            testResults.clear();
            discoveredHosts.clear();
            tableModel.setRowCount(0);
            detailArea.setText("选择一个测试结果查看详细信息...");
            updateFilters();
            updateStatusLabel();
        }
    }

    /**
     * 导出结果
     */
    private void exportResults() {
        if (testResults.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "没有测试结果可以导出", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出测试结果");
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("CSV文件", "csv"));

        if (fileChooser.showSaveDialog(mainPanel) == JFileChooser.APPROVE_OPTION) {
            try {
                exportToCsv(fileChooser.getSelectedFile());
                JOptionPane.showMessageDialog(mainPanel, "导出成功！", "提示", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(mainPanel, "导出失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 导出到CSV文件
     */
    private void exportToCsv(java.io.File file) throws java.io.IOException {
        try (java.io.PrintWriter writer = new java.io.PrintWriter(new java.io.FileWriter(file))) {
            // 写入表头
            writer.println("URL,方法,状态码,响应时间(ms),内容长度,测试时间,结果,主机,错误信息");

            // 写入数据
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (ApiTestResult result : testResults) {
                writer.printf("\"%s\",\"%s\",%d,%d,%d,\"%s\",\"%s\",\"%s\",\"%s\"%n",
                    result.getUrl(),
                    result.getMethod(),
                    result.getStatusCode(),
                    result.getResponseTime(),
                    result.getContentLength(),
                    result.getTestTime().format(formatter),
                    result.isSuccess() ? "成功" : "失败",
                    result.getHost(),
                    result.getErrorMessage() != null ? result.getErrorMessage() : ""
                );
            }
        }
    }

    /**
     * 全选/取消全选
     */
    private void selectAllResults(boolean selected) {
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            tableModel.setValueAt(selected, i, 0);
        }
    }

    /**
     * 复制选中的URL
     */
    private void copySelectedUrl() {
        int selectedRow = resultTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = resultTable.convertRowIndexToModel(selectedRow);
            String url = (String) tableModel.getValueAt(modelRow, 1);

            java.awt.datatransfer.StringSelection selection = new java.awt.datatransfer.StringSelection(url);
            java.awt.Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);

            JOptionPane.showMessageDialog(mainPanel, "URL已复制到剪贴板", "提示", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * 发送到Repeater
     */
    private void sendToRepeater() {
        int selectedRow = resultTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = resultTable.convertRowIndexToModel(selectedRow);
            String url = (String) tableModel.getValueAt(modelRow, 1);
            String method = (String) tableModel.getValueAt(modelRow, 2);

            // 查找对应的测试结果
            ApiTestResult result = testResults.stream()
                .filter(r -> r.getUrl().equals(url))
                .findFirst()
                .orElse(null);

            if (result != null) {
                try {
                    // 如果有完整的请求字节数据，直接使用
                    if (result.getRequestBytes() != null && result.getRequestBytes().length > 0) {
                        java.net.URL urlObj = new java.net.URL(result.getUrl());
                        boolean useHttps = "https".equals(urlObj.getProtocol());
                        int port = urlObj.getPort();
                        if (port == -1) {
                            port = useHttps ? 443 : 80;
                        }

                        // 使用Burp的sendToRepeater方法
                        callbacks.sendToRepeater(urlObj.getHost(), port, useHttps, result.getRequestBytes());
                        callbacks.printOutput("已发送到Repeater: " + method + " " + url);
                        JOptionPane.showMessageDialog(mainPanel, "已成功发送到Repeater", "提示", JOptionPane.INFORMATION_MESSAGE);
                    } else {
                        // 构建HTTP请求
                        String request = buildHttpRequest(result);
                        java.net.URL urlObj = new java.net.URL(result.getUrl());
                        boolean useHttps = "https".equals(urlObj.getProtocol());
                        int port = urlObj.getPort();
                        if (port == -1) {
                            port = useHttps ? 443 : 80;
                        }

                        callbacks.sendToRepeater(urlObj.getHost(), port, useHttps, request.getBytes());
                        callbacks.printOutput("已发送到Repeater: " + method + " " + url);
                        JOptionPane.showMessageDialog(mainPanel, "已成功发送到Repeater", "提示", JOptionPane.INFORMATION_MESSAGE);
                    }
                } catch (Exception e) {
                    callbacks.printError("发送到Repeater失败: " + e.getMessage());
                    JOptionPane.showMessageDialog(mainPanel, "发送失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        }
    }

    /**
     * 构建HTTP请求字符串
     */
    private String buildHttpRequest(ApiTestResult result) {
        StringBuilder request = new StringBuilder();

        try {
            java.net.URL urlObj = new java.net.URL(result.getUrl());
            String path = urlObj.getPath();
            if (urlObj.getQuery() != null) {
                path += "?" + urlObj.getQuery();
            }

            // 请求行
            request.append(result.getMethod()).append(" ").append(path).append(" HTTP/1.1\r\n");

            // Host头
            request.append("Host: ").append(urlObj.getHost());
            if (urlObj.getPort() != -1 && urlObj.getPort() != 80 && urlObj.getPort() != 443) {
                request.append(":").append(urlObj.getPort());
            }
            request.append("\r\n");

            // 其他请求头
            if (result.getRequestHeaders() != null && !result.getRequestHeaders().isEmpty()) {
                // 如果有保存的请求头，使用保存的
                String[] headers = result.getRequestHeaders().split("\n");
                for (String header : headers) {
                    if (!header.trim().isEmpty() && !header.toLowerCase().startsWith("host:")) {
                        request.append(header.trim()).append("\r\n");
                    }
                }
            } else {
                // 默认请求头
                request.append("User-Agent: Burp API Checker\r\n");
                request.append("Accept: */*\r\n");
                request.append("Connection: close\r\n");
            }

            // 请求体
            if (result.getRequestBody() != null && !result.getRequestBody().isEmpty()) {
                request.append("Content-Length: ").append(result.getRequestBody().getBytes().length).append("\r\n");
                request.append("\r\n");
                request.append(result.getRequestBody());
            } else {
                request.append("\r\n");
            }

        } catch (Exception e) {
            // 如果URL解析失败，构建简单请求
            request.append(result.getMethod()).append(" ").append(result.getUrl()).append(" HTTP/1.1\r\n");
            request.append("User-Agent: Burp API Checker\r\n");
            request.append("Accept: */*\r\n");
            request.append("Connection: close\r\n");
            request.append("\r\n");
        }

        return request.toString();
    }

    /**
     * 重新测试选中项
     */
    private void retestSelected() {
        int selectedRow = resultTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = resultTable.convertRowIndexToModel(selectedRow);
            String url = (String) tableModel.getValueAt(modelRow, 1);
            String method = (String) tableModel.getValueAt(modelRow, 2);

            // 这里可以调用API测试Tab的重新测试功能
            JOptionPane.showMessageDialog(mainPanel, "重新测试功能需要与API测试Tab集成", "提示", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * 移除选中项
     */
    private void removeSelected() {
        int selectedRow = resultTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = resultTable.convertRowIndexToModel(selectedRow);
            String url = (String) tableModel.getValueAt(modelRow, 1);

            // 从数据中移除
            testResults.removeIf(result -> result.getUrl().equals(url));

            // 从表格中移除
            tableModel.removeRow(modelRow);

            // 更新过滤器和状态
            updateFilters();
            updateStatusLabel();

            // 清除详情显示
            detailArea.setText("选择一个测试结果查看详细信息...");
        }
    }

    /**
     * 获取面板
     */
    public JPanel getPanel() {
        return mainPanel;
    }
}
