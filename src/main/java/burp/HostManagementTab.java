package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 主机管理Tab - 参考JsRouteScan的HostTab设计
 * 提供主机信息的集中管理和展示
 */
public class HostManagementTab {
    
    private IBurpExtenderCallbacks callbacks;
    private ApiScanMainTab mainTab;
    
    // UI组件
    private JPanel mainPanel;
    private JTable hostTable;
    private DefaultTableModel tableModel;
    private TableRowSorter<DefaultTableModel> sorter;
    private JTextArea detailArea;
    private JLabel statusLabel;
    
    // 数据
    private List<HostContent> hostList;
    
    // 表格列定义
    private static final String[] COLUMN_NAMES = {
        "选择", "主机", "协议", "路径数", "敏感信息", "API端点", "风险等级", "最后扫描", "状态"
    };
    
    public HostManagementTab(IBurpExtenderCallbacks callbacks, ApiScanMainTab mainTab) {
        this.callbacks = callbacks;
        this.mainTab = mainTab;
        this.hostList = new ArrayList<>();
        
        initializeUI();
    }
    
    /**
     * 初始化用户界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建顶部工具栏
        JPanel toolbarPanel = createToolbar();
        mainPanel.add(toolbarPanel, BorderLayout.NORTH);
        
        // 创建主要内容区域
        JSplitPane splitPane = createMainContent();
        mainPanel.add(splitPane, BorderLayout.CENTER);
        
        // 创建底部状态栏
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建工具栏
     */
    private JPanel createToolbar() {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        toolbar.setBorder(BorderFactory.createEtchedBorder());
        
        // 全选按钮
        JButton selectAllButton = new JButton("全选");
        selectAllButton.addActionListener(e -> selectAllHosts(true));
        toolbar.add(selectAllButton);
        
        // 取消全选按钮
        JButton deselectAllButton = new JButton("取消全选");
        deselectAllButton.addActionListener(e -> selectAllHosts(false));
        toolbar.add(deselectAllButton);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 刷新按钮
        JButton refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> refreshData());
        toolbar.add(refreshButton);
        
        // 清除按钮
        JButton clearButton = new JButton("清除");
        clearButton.addActionListener(e -> clearSelectedHosts());
        toolbar.add(clearButton);
        
        // 导出按钮
        JButton exportButton = new JButton("导出");
        exportButton.addActionListener(e -> exportHostData());
        toolbar.add(exportButton);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 搜索框
        JTextField searchField = new JTextField(15);
        searchField.setToolTipText("搜索主机...");
        searchField.addActionListener(e -> filterHosts(searchField.getText()));
        toolbar.add(new JLabel("搜索:"));
        toolbar.add(searchField);
        
        JButton searchButton = new JButton("搜索");
        searchButton.addActionListener(e -> filterHosts(searchField.getText()));
        toolbar.add(searchButton);
        
        return toolbar;
    }
    
    /**
     * 创建主要内容区域
     */
    private JSplitPane createMainContent() {
        // 创建表格
        tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 0) {
                    return Boolean.class; // 选择列
                }
                return String.class;
            }
            
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0; // 只有选择列可编辑
            }
        };
        
        hostTable = new JTable(tableModel);
        hostTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        hostTable.setAutoCreateRowSorter(true);
        
        // 设置列宽
        hostTable.getColumnModel().getColumn(0).setMaxWidth(50); // 选择列
        hostTable.getColumnModel().getColumn(2).setMaxWidth(80); // 协议列
        hostTable.getColumnModel().getColumn(3).setMaxWidth(80); // 路径数列
        hostTable.getColumnModel().getColumn(4).setMaxWidth(80); // 敏感信息列
        hostTable.getColumnModel().getColumn(5).setMaxWidth(80); // API端点列
        hostTable.getColumnModel().getColumn(6).setMaxWidth(80); // 风险等级列
        hostTable.getColumnModel().getColumn(8).setMaxWidth(80); // 状态列
        
        // 添加表格选择监听器
        hostTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showHostDetails();
            }
        });
        
        // 添加右键菜单
        addContextMenu();
        
        JScrollPane tableScrollPane = new JScrollPane(hostTable);
        tableScrollPane.setPreferredSize(new Dimension(800, 300));
        
        // 创建详情面板
        detailArea = new JTextArea();
        detailArea.setEditable(false);
        detailArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailArea.setText("选择主机以查看详细信息...");
        
        JScrollPane detailScrollPane = new JScrollPane(detailArea);
        detailScrollPane.setPreferredSize(new Dimension(800, 200));
        detailScrollPane.setBorder(BorderFactory.createTitledBorder("主机详情"));
        
        // 创建分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, tableScrollPane, detailScrollPane);
        splitPane.setDividerLocation(300);
        splitPane.setResizeWeight(0.6);
        
        return splitPane;
    }
    
    /**
     * 创建状态栏
     */
    private JPanel createStatusPanel() {
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        statusLabel = new JLabel("主机总数: 0 | 选中: 0 | 活跃: 0");
        statusPanel.add(statusLabel);
        
        return statusPanel;
    }
    
    /**
     * 添加右键菜单
     */
    private void addContextMenu() {
        JPopupMenu contextMenu = new JPopupMenu();
        
        JMenuItem viewDetailsItem = new JMenuItem("查看详情");
        viewDetailsItem.addActionListener(e -> showHostDetails());
        contextMenu.add(viewDetailsItem);
        
        JMenuItem copyHostItem = new JMenuItem("复制主机");
        copyHostItem.addActionListener(e -> copySelectedHost());
        contextMenu.add(copyHostItem);
        
        contextMenu.addSeparator();
        
        JMenuItem scanHostItem = new JMenuItem("扫描主机");
        scanHostItem.addActionListener(e -> scanSelectedHost());
        contextMenu.add(scanHostItem);
        
        JMenuItem removeHostItem = new JMenuItem("移除主机");
        removeHostItem.addActionListener(e -> removeSelectedHost());
        contextMenu.add(removeHostItem);
        
        hostTable.setComponentPopupMenu(contextMenu);
    }
    
    /**
     * 添加主机
     */
    public void addHost(HostContent hostContent) {
        if (!hostList.contains(hostContent)) {
            hostList.add(hostContent);
            updateTableRow(hostContent);
            updateStatusLabel();
        }
    }
    
    /**
     * 更新表格行
     */
    private void updateTableRow(HostContent hostContent) {
        SwingUtilities.invokeLater(() -> {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm");
            String lastScanTime = hostContent.getLastScanTime().format(formatter);
            
            Object[] rowData = {
                false, // 选择状态
                hostContent.getHost(),
                hostContent.getProtocol().toUpperCase(),
                hostContent.getRouteCount(),
                hostContent.getSensitiveDataCount(),
                hostContent.getApiEndpointCount(),
                hostContent.getRiskLevel(),
                lastScanTime,
                hostContent.isActive() ? "活跃" : "非活跃"
            };
            
            tableModel.addRow(rowData);
        });
    }
    
    /**
     * 显示主机详情
     */
    private void showHostDetails() {
        int selectedRow = hostTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = hostTable.convertRowIndexToModel(selectedRow);
            String hostName = (String) tableModel.getValueAt(modelRow, 1);
            
            HostContent hostContent = findHostByName(hostName);
            if (hostContent != null) {
                displayHostDetails(hostContent);
            }
        }
    }
    
    /**
     * 显示主机详细信息
     */
    private void displayHostDetails(HostContent hostContent) {
        StringBuilder details = new StringBuilder();
        details.append("主机信息详情\n");
        for (int i = 0; i < 50; i++) details.append("=");
        details.append("\n\n");
        
        details.append("基本信息:\n");
        details.append("  主机: ").append(hostContent.getHost()).append("\n");
        details.append("  协议: ").append(hostContent.getProtocol().toUpperCase()).append("\n");
        details.append("  端口: ").append(hostContent.getPort()).append("\n");
        details.append("  完整URL: ").append(hostContent.getFullUrl()).append("\n");
        details.append("  状态: ").append(hostContent.isActive() ? "活跃" : "非活跃").append("\n\n");
        
        details.append("统计信息:\n");
        details.append("  路径总数: ").append(hostContent.getRouteCount()).append("\n");
        details.append("  敏感信息: ").append(hostContent.getSensitiveDataCount()).append("\n");
        details.append("  API端点: ").append(hostContent.getApiEndpointCount()).append("\n");
        details.append("  JS文件: ").append(hostContent.getJsFileCount()).append("\n");
        details.append("  总请求数: ").append(hostContent.getTotalRequests()).append("\n");
        details.append("  风险等级: ").append(hostContent.getRiskLevel()).append("\n\n");
        
        details.append("时间信息:\n");
        details.append("  最后扫描: ").append(hostContent.getLastScanTime()).append("\n\n");
        
        if (!hostContent.getHeaders().isEmpty()) {
            details.append("HTTP头信息:\n");
            hostContent.getHeaders().forEach((key, value) -> 
                details.append("  ").append(key).append(": ").append(value).append("\n"));
            details.append("\n");
        }
        
        details.append("路径列表 (前10个):\n");
        hostContent.getRoutes().stream()
            .limit(10)
            .forEach(route -> details.append("  ").append(route.getPath()).append("\n"));
        
        if (hostContent.getRouteCount() > 10) {
            details.append("  ... 还有 ").append(hostContent.getRouteCount() - 10).append(" 个路径\n");
        }
        
        detailArea.setText(details.toString());
        detailArea.setCaretPosition(0);
    }
    
    /**
     * 根据名称查找主机
     */
    private HostContent findHostByName(String hostName) {
        return hostList.stream()
            .filter(host -> host.getHost().equals(hostName))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 全选/取消全选主机
     */
    private void selectAllHosts(boolean select) {
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            tableModel.setValueAt(select, i, 0);
        }
        updateStatusLabel();
    }
    
    /**
     * 过滤主机
     */
    private void filterHosts(String searchText) {
        if (searchText.trim().isEmpty()) {
            hostTable.setRowSorter(null);
        } else {
            RowFilter<DefaultTableModel, Object> filter = RowFilter.regexFilter("(?i)" + searchText);
            sorter = new TableRowSorter<>(tableModel);
            sorter.setRowFilter(filter);
            hostTable.setRowSorter(sorter);
        }
    }
    
    /**
     * 复制选中的主机
     */
    private void copySelectedHost() {
        int selectedRow = hostTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = hostTable.convertRowIndexToModel(selectedRow);
            String hostName = (String) tableModel.getValueAt(modelRow, 1);
            
            Toolkit.getDefaultToolkit().getSystemClipboard()
                .setContents(new java.awt.datatransfer.StringSelection(hostName), null);
            
            callbacks.printOutput("已复制主机: " + hostName);
        }
    }
    
    /**
     * 扫描选中的主机
     */
    private void scanSelectedHost() {
        int selectedRow = hostTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = hostTable.convertRowIndexToModel(selectedRow);
            String hostName = (String) tableModel.getValueAt(modelRow, 1);
            
            // 这里可以触发对特定主机的扫描
            callbacks.printOutput("开始扫描主机: " + hostName);
            // TODO: 实现主机扫描逻辑
        }
    }
    
    /**
     * 移除选中的主机
     */
    private void removeSelectedHost() {
        int selectedRow = hostTable.getSelectedRow();
        if (selectedRow >= 0) {
            int result = JOptionPane.showConfirmDialog(
                mainPanel,
                "确定要移除选中的主机吗？",
                "确认移除",
                JOptionPane.YES_NO_OPTION
            );
            
            if (result == JOptionPane.YES_OPTION) {
                int modelRow = hostTable.convertRowIndexToModel(selectedRow);
                String hostName = (String) tableModel.getValueAt(modelRow, 1);
                
                // 从数据中移除
                hostList.removeIf(host -> host.getHost().equals(hostName));
                
                // 从表格中移除
                tableModel.removeRow(modelRow);
                
                updateStatusLabel();
                callbacks.printOutput("已移除主机: " + hostName);
            }
        }
    }
    
    /**
     * 清除选中的主机
     */
    private void clearSelectedHosts() {
        List<Integer> selectedRows = new ArrayList<>();
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            Boolean selected = (Boolean) tableModel.getValueAt(i, 0);
            if (selected != null && selected) {
                selectedRows.add(i);
            }
        }
        
        if (selectedRows.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请先选择要清除的主机");
            return;
        }
        
        int result = JOptionPane.showConfirmDialog(
            mainPanel,
            "确定要清除 " + selectedRows.size() + " 个选中的主机吗？",
            "确认清除",
            JOptionPane.YES_NO_OPTION
        );
        
        if (result == JOptionPane.YES_OPTION) {
            // 从后往前删除，避免索引变化
            for (int i = selectedRows.size() - 1; i >= 0; i--) {
                int row = selectedRows.get(i);
                String hostName = (String) tableModel.getValueAt(row, 1);
                
                // 从数据中移除
                hostList.removeIf(host -> host.getHost().equals(hostName));
                
                // 从表格中移除
                tableModel.removeRow(row);
            }
            
            updateStatusLabel();
            callbacks.printOutput("已清除 " + selectedRows.size() + " 个主机");
        }
    }
    
    /**
     * 导出主机数据
     */
    private void exportHostData() {
        // TODO: 实现主机数据导出功能
        callbacks.printOutput("导出主机数据功能待实现");
    }
    
    /**
     * 更新状态标签
     */
    private void updateStatusLabel() {
        SwingUtilities.invokeLater(() -> {
            int totalHosts = hostList.size();
            int activeHosts = (int) hostList.stream().filter(HostContent::isActive).count();
            
            int selectedCount = 0;
            for (int i = 0; i < tableModel.getRowCount(); i++) {
                Boolean selected = (Boolean) tableModel.getValueAt(i, 0);
                if (selected != null && selected) {
                    selectedCount++;
                }
            }
            
            statusLabel.setText(String.format("主机总数: %d | 选中: %d | 活跃: %d", 
                totalHosts, selectedCount, activeHosts));
        });
    }
    
    /**
     * 刷新数据
     */
    public void refreshData() {
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            for (HostContent hostContent : hostList) {
                updateTableRow(hostContent);
            }
            updateStatusLabel();
        });
    }
    
    /**
     * 清除数据
     */
    public void clearData() {
        hostList.clear();
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            detailArea.setText("选择主机以查看详细信息...");
            updateStatusLabel();
        });
    }
    
    /**
     * 获取面板
     */
    public JPanel getPanel() {
        return mainPanel;
    }
}
