package burp;

import javax.swing.*;
import java.awt.*;
import java.util.*;
import java.util.List;

/**
 * API扫描主界面 - 参考JsRouteScan的设计理念
 * 提供模块化的API安全测试功能
 */
public class ApiScanMainTab implements ITab, IPassiveAnalysisReceiver {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    
    // 主界面组件
    private JPanel mainPanel;
    private JTabbedPane mainTabs;
    
    // 各个功能Tab
    private ApiDiscoveryTab discoveryTab;
    private PathDiscoveryTab pathTab;
    private SensitiveInfoTab infoTab;
    private ApiTestingTab testTab;
    
    // 数据管理
    private Map<String, HostContent> hostMap;
    private List<RouteContent> routeList;
    private List<EnhancedSensitiveInfoExtractor.SensitiveDataItem> sensitiveDataList;
    
    public ApiScanMainTab(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        
        // 初始化数据结构
        this.hostMap = new HashMap<>();
        this.routeList = new ArrayList<>();
        this.sensitiveDataList = new ArrayList<>();
        
        // 初始化界面
        initializeUI();
    }
    
    /**
     * 初始化用户界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建主标签页
        mainTabs = new JTabbedPane();
        
        // 初始化各个功能Tab
        initializeTabs();
        
        // 添加标签页
        mainTabs.addTab("API发现", discoveryTab.getPanel());
        mainTabs.addTab("路径发现", pathTab.getPanel());
        mainTabs.addTab("敏感信息", infoTab.getPanel());
        mainTabs.addTab("API测试", testTab.getPanel());
        
        // 添加标签页切换监听器
        mainTabs.addChangeListener(e -> onTabChanged());
        
        mainPanel.add(mainTabs, BorderLayout.CENTER);
        
        // 添加状态栏
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 初始化各个功能Tab
     */
    private void initializeTabs() {
        discoveryTab = new ApiDiscoveryTab(callbacks, this);
        pathTab = new PathDiscoveryTab(callbacks, this);
        infoTab = new SensitiveInfoTab(callbacks, this);
        testTab = new ApiTestingTab(callbacks, this);
    }


    
    /**
     * 创建状态栏
     */
    private JPanel createStatusPanel() {
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JLabel statusLabel = new JLabel("就绪 - API扫描工具已启动");
        statusPanel.add(statusLabel);
        
        // 添加统计信息
        JLabel statsLabel = new JLabel("主机: 0 | 路径: 0 | 敏感信息: 0");
        statusPanel.add(Box.createHorizontalStrut(20));
        statusPanel.add(statsLabel);
        
        return statusPanel;
    }
    
    /**
     * 标签页切换事件处理
     */
    private void onTabChanged() {
        int selectedIndex = mainTabs.getSelectedIndex();
        String tabName = mainTabs.getTitleAt(selectedIndex);
        
        // 根据切换的标签页执行相应操作
        switch (selectedIndex) {
            case 0: // API发现
                // discoveryTab.refreshData(); // API发现Tab不需要刷新
                break;
            case 1: // 路径发现
                pathTab.refreshData();
                break;
            case 2: // 敏感信息
                infoTab.refreshData();
                break;
            case 3: // API测试
                testTab.refreshData();
                break;
        }
        
        callbacks.printOutput("切换到标签页: " + tabName);
    }
    
    /**
     * 添加主机信息
     */
    public void addHost(String host) {
        if (!hostMap.containsKey(host)) {
            HostContent hostContent = new HostContent(host);
            hostMap.put(host, hostContent);
            
            // 主机信息已存储在hostMap中，其他Tab可以通过getHostMap()访问
            
            callbacks.printOutput("发现新主机: " + host);
        }
    }
    
    /**
     * 添加路径信息
     */
    public void addRoute(RouteContent route) {
        routeList.add(route);

        // 确保主机存在于hostMap中
        String host = route.getHost();
        if (!hostMap.containsKey(host)) {
            addHost(host);
        }

        // 更新主机信息
        HostContent hostContent = hostMap.get(host);
        if (hostContent != null) {
            hostContent.addRoute(route);
        }

        // 通知路径发现Tab更新
        pathTab.addRoute(route);

        // 通知API测试Tab刷新主机列表
        testTab.refreshData();

        callbacks.printOutput("发现新路径: " + route.getHost() + route.getPath());
    }
    
    /**
     * 添加敏感信息
     */
    public void addSensitiveData(EnhancedSensitiveInfoExtractor.SensitiveDataItem item) {
        sensitiveDataList.add(item);

        // 确保主机存在于hostMap中
        String host = extractHost(item.url);
        if (!hostMap.containsKey(host)) {
            addHost(host);
        }

        // 更新主机信息
        HostContent hostContent = hostMap.get(host);
        if (hostContent != null) {
            hostContent.addSensitiveData(item);
        }

        // 通知敏感信息Tab更新
        infoTab.addSensitiveData(item);

        // 通知API测试Tab刷新主机列表
        testTab.refreshData();

        callbacks.printOutput("发现敏感信息: " + item.type + " 在 " + host);
    }
    
    /**
     * 从URL中提取主机信息 - 专门针对Burp Suite的HTTP/HTTPS流量
     */
    private String extractHost(String url) {
        try {
            if (url == null || url.trim().isEmpty()) {
                return "未知主机";
            }

            String trimmedUrl = url.trim();

            // Burp只处理HTTP/HTTPS流量，所以URL应该总是以http://或https://开头
            if (trimmedUrl.startsWith("http://") || trimmedUrl.startsWith("https://")) {
                java.net.URL urlObj = new java.net.URL(trimmedUrl);
                String host = urlObj.getHost();

                if (host == null || host.trim().isEmpty()) {
                    return "未知主机";
                }

                int port = urlObj.getPort();

                // 如果端口不是默认端口，则包含端口号
                if (port != -1 && port != 80 && port != 443) {
                    return host + ":" + port;
                }

                return host;
            } else {
                // 这种情况不应该发生，因为Burp只处理HTTP/HTTPS
                return "非HTTP协议";
            }

        } catch (Exception e) {
            callbacks.printError("提取主机信息失败: " + url + " - " + e.getMessage());
            return "解析失败";
        }
    }
    
    /**
     * 获取主机列表
     */
    public Map<String, HostContent> getHostMap() {
        return hostMap;
    }
    
    /**
     * 获取路径列表
     */
    public List<RouteContent> getRouteList() {
        return routeList;
    }
    
    /**
     * 获取敏感信息列表
     */
    public List<EnhancedSensitiveInfoExtractor.SensitiveDataItem> getSensitiveDataList() {
        return sensitiveDataList;
    }

    /**
     * 添加API测试结果 - 已集成到API测试Tab中
     */
    public void addTestResult(ApiTestResultTab.ApiTestResult result) {
        // ✅ 测试结果功能已集成到API测试Tab中，无需单独处理
        // 结果会直接在API测试Tab中显示
    }

    /**
     * 清除所有数据
     */
    public void clearAllData() {
        hostMap.clear();
        routeList.clear();
        sensitiveDataList.clear();
        
        // 通知各个Tab清除数据
        // discoveryTab不需要清除数据方法
        pathTab.clearData();
        infoTab.clearData();
        testTab.clearData();

        callbacks.printOutput("已清除所有数据");
    }
    
    /**
     * 更新统计信息
     */
    public void updateStatistics() {
        SwingUtilities.invokeLater(() -> {
            // 更新状态栏统计信息
            // 这里可以添加统计信息更新逻辑
        });
    }
    
    // ITab接口实现
    @Override
    public String getTabCaption() {
        return "API扫描";
    }

    @Override
    public Component getUiComponent() {
        return mainPanel;
    }

    // IPassiveAnalysisReceiver接口实现
    @Override
    public String getReceiverName() {
        return "API扫描主界面";
    }

    @Override
    public void onApiPathDiscovered(String apiPath, String sourceUrl, String method) {
        try {
            // 提取主机信息
            String host = extractHost(sourceUrl);

            // 创建RouteContent对象
            RouteContent route = new RouteContent(host, apiPath, sourceUrl, "被动分析");
            route.addMethod(method != null ? method : "GET");

            // 添加到路径列表
            addRoute(route);

            callbacks.printOutput("[被动分析] 发现API路径: " + apiPath + " (来源: " + sourceUrl + ")");
        } catch (Exception e) {
            callbacks.printError("处理被动发现的API路径时出错: " + e.getMessage());
        }
    }

    @Override
    public void onSensitiveInfoDiscovered(String type, String value, String sourceUrl, String context) {
        try {
            // 提取主机信息
            String host = extractHost(sourceUrl);

            // 创建SensitiveDataItem对象 - 修复参数顺序
            // 构造函数参数顺序：(type, value, source, url, description)
            EnhancedSensitiveInfoExtractor.SensitiveDataItem item =
                new EnhancedSensitiveInfoExtractor.SensitiveDataItem(
                    type, value, sourceUrl, sourceUrl, "[被动分析] " + context);

            // 添加到敏感信息列表
            addSensitiveData(item);

            callbacks.printOutput("[被动分析] 发现敏感信息 [" + type + "]: " +
                                maskSensitiveValue(value) + " (来源: " + sourceUrl + ", 主机: " + host + ")");
        } catch (Exception e) {
            callbacks.printError("处理被动发现的敏感信息时出错: " + e.getMessage());
        }
    }

    @Override
    public void onJavaScriptFileDiscovered(String jsUrl, String sourceUrl) {
        try {
            // 通知API发现Tab处理JavaScript文件
            if (discoveryTab != null) {
                // 这里可以添加JavaScript文件到发现列表
                callbacks.printOutput("[被动分析] 发现JS文件: " + jsUrl + " (来源: " + sourceUrl + ")");
            }
        } catch (Exception e) {
            callbacks.printError("处理被动发现的JS文件时出错: " + e.getMessage());
        }
    }

    @Override
    public void onDomainDiscovered(String domain, String sourceUrl) {
        try {
            // 添加主机信息
            addHost(domain);

            callbacks.printOutput("[被动分析] 发现域名: " + domain + " (来源: " + sourceUrl + ")");
        } catch (Exception e) {
            callbacks.printError("处理被动发现的域名时出错: " + e.getMessage());
        }
    }

    /**
     * 掩码敏感信息值用于日志输出
     */
    private String maskSensitiveValue(String value) {
        if (value == null || value.length() <= 6) {
            return "***";
        }
        return value.substring(0, 3) + "***" + value.substring(value.length() - 3);
    }
}
