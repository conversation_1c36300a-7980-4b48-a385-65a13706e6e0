package burp;

import java.util.*;
import java.net.URL;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

public class ApiUtils {
    private static final Pattern API_PATTERN = Pattern.compile("(?i)(/api/|api/|/api$|/api\\?|/api\\.|/api/|/apis/|/api\\d+/|/v\\d+/api/|/rest/|/restapi/|/service/|/services/|/\\w+api/)");
    private static final Pattern PARAMETER_PATTERN = Pattern.compile("(?:[?&]([^=&]+)=|\\{([^}]+)\\}|:([^/]+))");
    
    public static List<String> extractApiEndpoints(String content) {
        List<String> endpoints = new ArrayList<>();
        Matcher matcher = API_PATTERN.matcher(content);
        
        while (matcher.find()) {
            String endpoint = matcher.group();
            if (!endpoints.contains(endpoint)) {
                endpoints.add(endpoint);
            }
        }
        
        return endpoints;
    }
    
    public static List<String> extractParameters(String url) {
        List<String> parameters = new ArrayList<>();
        Matcher matcher = PARAMETER_PATTERN.matcher(url);
        
        while (matcher.find()) {
            String param = null;
            if (matcher.group(1) != null) param = matcher.group(1);
            else if (matcher.group(2) != null) param = matcher.group(2);
            else if (matcher.group(3) != null) param = matcher.group(3);
            
            if (param != null && !parameters.contains(param)) {
                parameters.add(param);
            }
        }
        
        return parameters;
    }
    
    public static boolean isApiUrl(String url) {
        return API_PATTERN.matcher(url).find();
    }
    
    public static String normalizeUrl(String baseUrl, String path) {
        try {
            if (path.startsWith("http://") || path.startsWith("https://")) {
                return path;
            }
            return new URL(new URL(baseUrl), path).toString();
        } catch (Exception e) {
            return path;
        }
    }
    
    public static Map<String, String> analyzeResponse(byte[] response, String contentType) {
        Map<String, String> result = new HashMap<>();
        result.put("content_type", contentType);
        
        String responseStr = new String(response);
        if (contentType.contains("json")) {
            result.put("type", "json");
            // Add JSON-specific analysis
        } else if (contentType.contains("xml")) {
            result.put("type", "xml");
            // Add XML-specific analysis
        }
        
        result.put("length", String.valueOf(response.length));
        result.put("hash", String.valueOf(responseStr.hashCode()));
        
        return result;
    }
} 