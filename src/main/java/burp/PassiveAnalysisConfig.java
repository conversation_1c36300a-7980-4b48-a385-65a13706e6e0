package burp;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 被动分析配置类
 * 统一管理所有被动分析功能的开关和参数
 */
public class PassiveAnalysisConfig {
    
    // 全局开关
    private final AtomicBoolean globalEnabled = new AtomicBoolean(true);
    
    // 功能开关
    private final AtomicBoolean apiDiscoveryEnabled = new AtomicBoolean(true);
    private final AtomicBoolean sensitiveInfoEnabled = new AtomicBoolean(true);
    private final AtomicBoolean jsAnalysisEnabled = new AtomicBoolean(true);
    private final AtomicBoolean domainCollectionEnabled = new AtomicBoolean(true);
    
    // 过滤配置
    private final AtomicBoolean domainFilterEnabled = new AtomicBoolean(false);
    private final Set<String> includeDomains = ConcurrentHashMap.newKeySet();
    private final Set<String> excludeDomains = ConcurrentHashMap.newKeySet();
    private final Set<String> excludeExtensions = ConcurrentHashMap.newKeySet();
    
    // 性能配置
    private final AtomicInteger maxUrlsPerMinute = new AtomicInteger(100);
    private final AtomicInteger maxResponseSize = new AtomicInteger(1024 * 1024); // 1MB
    private final AtomicBoolean asyncProcessing = new AtomicBoolean(true);
    
    // 去重配置
    private final Set<String> processedUrls = ConcurrentHashMap.newKeySet();
    private final AtomicInteger maxProcessedUrls = new AtomicInteger(10000);
    
    // 统计信息
    private final AtomicInteger totalProcessed = new AtomicInteger(0);
    private final AtomicInteger apiPathsFound = new AtomicInteger(0);
    private final AtomicInteger sensitiveInfoFound = new AtomicInteger(0);
    private final AtomicInteger jsFilesFound = new AtomicInteger(0);
    
    public PassiveAnalysisConfig() {
        // 初始化默认排除的文件扩展名
        excludeExtensions.add(".css");
        excludeExtensions.add(".png");
        excludeExtensions.add(".jpg");
        excludeExtensions.add(".jpeg");
        excludeExtensions.add(".gif");
        excludeExtensions.add(".ico");
        excludeExtensions.add(".svg");
        excludeExtensions.add(".woff");
        excludeExtensions.add(".woff2");
        excludeExtensions.add(".ttf");
        excludeExtensions.add(".eot");
    }
    
    // 全局开关
    public boolean isGlobalEnabled() {
        return globalEnabled.get();
    }
    
    public void setGlobalEnabled(boolean enabled) {
        globalEnabled.set(enabled);
    }
    
    // 功能开关
    public boolean isApiDiscoveryEnabled() {
        return globalEnabled.get() && apiDiscoveryEnabled.get();
    }
    
    public void setApiDiscoveryEnabled(boolean enabled) {
        apiDiscoveryEnabled.set(enabled);
    }
    
    public boolean isSensitiveInfoEnabled() {
        return globalEnabled.get() && sensitiveInfoEnabled.get();
    }
    
    public void setSensitiveInfoEnabled(boolean enabled) {
        sensitiveInfoEnabled.set(enabled);
    }
    
    public boolean isJsAnalysisEnabled() {
        return globalEnabled.get() && jsAnalysisEnabled.get();
    }
    
    public void setJsAnalysisEnabled(boolean enabled) {
        jsAnalysisEnabled.set(enabled);
    }
    
    public boolean isDomainCollectionEnabled() {
        return globalEnabled.get() && domainCollectionEnabled.get();
    }
    
    public void setDomainCollectionEnabled(boolean enabled) {
        domainCollectionEnabled.set(enabled);
    }
    
    // 过滤配置
    public boolean isDomainFilterEnabled() {
        return domainFilterEnabled.get();
    }
    
    public void setDomainFilterEnabled(boolean enabled) {
        domainFilterEnabled.set(enabled);
    }
    
    public Set<String> getIncludeDomains() {
        return new HashSet<>(includeDomains);
    }
    
    public void addIncludeDomain(String domain) {
        if (domain != null && !domain.trim().isEmpty()) {
            includeDomains.add(domain.trim().toLowerCase());
        }
    }
    
    public void removeIncludeDomain(String domain) {
        if (domain != null) {
            includeDomains.remove(domain.trim().toLowerCase());
        }
    }
    
    public void clearIncludeDomains() {
        includeDomains.clear();
    }
    
    public Set<String> getExcludeDomains() {
        return new HashSet<>(excludeDomains);
    }
    
    public void addExcludeDomain(String domain) {
        if (domain != null && !domain.trim().isEmpty()) {
            excludeDomains.add(domain.trim().toLowerCase());
        }
    }
    
    public void removeExcludeDomain(String domain) {
        if (domain != null) {
            excludeDomains.remove(domain.trim().toLowerCase());
        }
    }
    
    public void clearExcludeDomains() {
        excludeDomains.clear();
    }
    
    public Set<String> getExcludeExtensions() {
        return new HashSet<>(excludeExtensions);
    }
    
    public void addExcludeExtension(String extension) {
        if (extension != null && !extension.trim().isEmpty()) {
            String ext = extension.trim().toLowerCase();
            if (!ext.startsWith(".")) {
                ext = "." + ext;
            }
            excludeExtensions.add(ext);
        }
    }
    
    public void removeExcludeExtension(String extension) {
        if (extension != null) {
            String ext = extension.trim().toLowerCase();
            if (!ext.startsWith(".")) {
                ext = "." + ext;
            }
            excludeExtensions.remove(ext);
        }
    }
    
    // 性能配置
    public int getMaxUrlsPerMinute() {
        return maxUrlsPerMinute.get();
    }
    
    public void setMaxUrlsPerMinute(int max) {
        maxUrlsPerMinute.set(Math.max(1, max));
    }
    
    public int getMaxResponseSize() {
        return maxResponseSize.get();
    }
    
    public void setMaxResponseSize(int size) {
        maxResponseSize.set(Math.max(1024, size)); // 最小1KB
    }
    
    public boolean isAsyncProcessing() {
        return asyncProcessing.get();
    }
    
    public void setAsyncProcessing(boolean async) {
        asyncProcessing.set(async);
    }
    
    // 去重管理
    public boolean isUrlProcessed(String url) {
        return processedUrls.contains(url);
    }
    
    public void markUrlProcessed(String url) {
        if (processedUrls.size() >= maxProcessedUrls.get()) {
            // 清理一半的记录，保持内存使用合理
            Set<String> toRemove = new HashSet<>();
            int count = 0;
            for (String processedUrl : processedUrls) {
                toRemove.add(processedUrl);
                if (++count >= maxProcessedUrls.get() / 2) {
                    break;
                }
            }
            processedUrls.removeAll(toRemove);
        }
        processedUrls.add(url);
    }
    
    public void clearProcessedUrls() {
        processedUrls.clear();
    }
    
    public int getMaxProcessedUrls() {
        return maxProcessedUrls.get();
    }
    
    public void setMaxProcessedUrls(int max) {
        maxProcessedUrls.set(Math.max(1000, max));
    }
    
    // 统计信息
    public int getTotalProcessed() {
        return totalProcessed.get();
    }
    
    public void incrementTotalProcessed() {
        totalProcessed.incrementAndGet();
    }
    
    public int getApiPathsFound() {
        return apiPathsFound.get();
    }
    
    public void incrementApiPathsFound() {
        apiPathsFound.incrementAndGet();
    }
    
    public int getSensitiveInfoFound() {
        return sensitiveInfoFound.get();
    }
    
    public void incrementSensitiveInfoFound() {
        sensitiveInfoFound.incrementAndGet();
    }
    
    public int getJsFilesFound() {
        return jsFilesFound.get();
    }
    
    public void incrementJsFilesFound() {
        jsFilesFound.incrementAndGet();
    }
    
    public void resetStatistics() {
        totalProcessed.set(0);
        apiPathsFound.set(0);
        sensitiveInfoFound.set(0);
        jsFilesFound.set(0);
    }
    
    /**
     * 检查URL是否应该被处理
     */
    public boolean shouldProcessUrl(String url) {
        if (!globalEnabled.get() || url == null || url.trim().isEmpty()) {
            return false;
        }
        
        try {
            java.net.URL urlObj = new java.net.URL(url);
            String host = urlObj.getHost().toLowerCase();
            String path = urlObj.getPath().toLowerCase();
            
            // 检查排除的文件扩展名
            for (String ext : excludeExtensions) {
                if (path.endsWith(ext)) {
                    return false;
                }
            }
            
            // 检查域名过滤
            if (domainFilterEnabled.get()) {
                // 如果有包含域名列表，只处理列表中的域名
                if (!includeDomains.isEmpty()) {
                    boolean matched = false;
                    for (String includeDomain : includeDomains) {
                        if (matchesDomainPattern(host, includeDomain)) {
                            matched = true;
                            break;
                        }
                    }
                    if (!matched) {
                        return false;
                    }
                }
                
                // 检查排除域名列表
                for (String excludeDomain : excludeDomains) {
                    if (matchesDomainPattern(host, excludeDomain)) {
                        return false;
                    }
                }
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 域名模式匹配
     */
    private boolean matchesDomainPattern(String domain, String pattern) {
        if (pattern.contains("*")) {
            String regex = pattern.replace(".", "\\.").replace("*", ".*");
            try {
                return domain.matches(regex);
            } catch (Exception e) {
                return domain.contains(pattern.replace("*", ""));
            }
        } else {
            return domain.equals(pattern) || domain.endsWith("." + pattern);
        }
    }
    
    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("被动分析配置摘要:\n");
        summary.append("- 全局开关: ").append(globalEnabled.get() ? "启用" : "禁用").append("\n");
        summary.append("- API发现: ").append(apiDiscoveryEnabled.get() ? "启用" : "禁用").append("\n");
        summary.append("- 敏感信息: ").append(sensitiveInfoEnabled.get() ? "启用" : "禁用").append("\n");
        summary.append("- JS分析: ").append(jsAnalysisEnabled.get() ? "启用" : "禁用").append("\n");
        summary.append("- 域名收集: ").append(domainCollectionEnabled.get() ? "启用" : "禁用").append("\n");
        summary.append("- 域名过滤: ").append(domainFilterEnabled.get() ? "启用" : "禁用").append("\n");
        summary.append("- 包含域名: ").append(includeDomains.size()).append(" 个\n");
        summary.append("- 排除域名: ").append(excludeDomains.size()).append(" 个\n");
        summary.append("- 排除扩展名: ").append(excludeExtensions.size()).append(" 个\n");
        summary.append("- 异步处理: ").append(asyncProcessing.get() ? "启用" : "禁用").append("\n");
        summary.append("- 已处理URL: ").append(processedUrls.size()).append(" 个\n");
        summary.append("- 统计: 总计").append(totalProcessed.get())
               .append(", API").append(apiPathsFound.get())
               .append(", 敏感信息").append(sensitiveInfoFound.get())
               .append(", JS文件").append(jsFilesFound.get()).append("\n");
        return summary.toString();
    }
}
