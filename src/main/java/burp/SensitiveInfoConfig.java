package burp;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 敏感信息配置管理器 - 统一管理所有敏感信息分类和检测规则
 * 确保分类显示和实际检测规则一一对应
 */
public class SensitiveInfoConfig {
    
    // 敏感信息模式类
    public static class SensitivePattern {
        public final String name;           // 显示名称
        public final Pattern pattern;       // 检测正则
        public final String description;    // 详细描述
        public final String category;       // 分类
        public final int riskLevel;         // 风险等级 1-5
        
        public SensitivePattern(String name, String regex, String description, String category, int riskLevel) {
            this.name = name;
            this.pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
            this.description = description;
            this.category = category;
            this.riskLevel = riskLevel;
        }
    }
    
    // 统一的敏感信息检测规则
    public static final SensitivePattern[] SENSITIVE_PATTERNS = {
        // === 身份信息类 (高风险) ===
        new SensitivePattern("身份证号", 
            "\\b([1-9]\\d{5}(19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx])\\b", 
            "中国大陆身份证号码", "身份信息", 5),
            
        new SensitivePattern("手机号码", 
            "\\b(1[3-9]\\d{9})\\b", 
            "中国大陆手机号码", "身份信息", 4),
            
        new SensitivePattern("邮箱地址", 
            "\\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})\\b", 
            "电子邮箱地址", "身份信息", 3),

        // === 网络信息类 (中风险) ===
        new SensitivePattern("IPv4地址", 
            "\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b", 
            "IPv4网络地址", "网络信息", 2),
            
        new SensitivePattern("IP+端口", 
            "\\b((?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5})\\b", 
            "IP地址和端口组合", "网络信息", 3),
            
        new SensitivePattern("内网IP", 
            "\\b(?:10\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|172\\.(?:1[6-9]|2[0-9]|3[0-1])\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|192\\.168\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))\\b", 
            "内网IP地址", "网络信息", 4),
            
        new SensitivePattern("域名", 
            "\\b([a-zA-Z0-9](?:[a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+(com|cn|net|org|gov|edu|mil|int|co|io|me|tv|cc|tk|ml|ga|cf|biz|info|name|mobi|asia|tel|travel|museum|aero|jobs|cat|pro|xxx|post|geo|local)\\b", 
            "域名地址", "网络信息", 2),

        // === 认证凭据类 (极高风险) ===
        new SensitivePattern("JWT Token", 
            "\\b(eyJ[A-Za-z0-9_/+\\-]{10,}\\.[A-Za-z0-9_/+\\-]{15,}\\.[A-Za-z0-9_/+\\-]{10,})\\b", 
            "JSON Web Token", "认证凭据", 5),
            
        new SensitivePattern("Bearer Token", 
            "\\b[Bb]earer\\s+([a-zA-Z0-9\\-=._+/\\\\]{20,500})\\b", 
            "Bearer认证令牌", "认证凭据", 5),
            
        new SensitivePattern("Basic Auth", 
            "\\b[Bb]asic\\s+([A-Za-z0-9+/]{18,}={0,2})\\b", 
            "Basic认证凭据", "认证凭据", 5),
            
        new SensitivePattern("Authorization Token", 
            "[\"'\\[]*[Aa]uthorization[\"'\\]]*\\s*[:=]\\s*['\"]?\\b(?:[Tt]oken\\s+)?([a-zA-Z0-9\\-_+/]{20,500})['\"]?", 
            "认证令牌", "认证凭据", 5),

        // === 云服务密钥类 (极高风险) ===
        new SensitivePattern("阿里云AccessKey", 
            "\\b(LTAI[A-Za-z\\d]{12,30})\\b", 
            "阿里云AccessKey ID", "云服务密钥", 5),
            
        new SensitivePattern("腾讯云SecretKey", 
            "\\b(AKID[A-Za-z0-9]{13,40})\\b", 
            "腾讯云SecretKey", "云服务密钥", 5),
            
        new SensitivePattern("AWS AccessKey", 
            "\\b((?:A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16})\\b", 
            "AWS访问密钥", "云服务密钥", 5),
            
        new SensitivePattern("GitHub Token", 
            "\\b((?:ghp|gho|ghu|ghs|ghr|github_pat)_[a-zA-Z0-9_]{36,255})\\b", 
            "GitHub访问令牌", "云服务密钥", 5),
            
        new SensitivePattern("Google API Key", 
            "\\b(AIza[0-9A-Za-z_\\-]{35})\\b", 
            "Google API密钥", "云服务密钥", 5),

        // === 数据库配置类 (极高风险) ===
        new SensitivePattern("JDBC连接", 
            "\\b(jdbc:[a-zA-Z0-9]+://[^\\s\"'<>]+)\\b", 
            "数据库连接字符串", "数据库配置", 5),
            
        new SensitivePattern("私钥", 
            "(-----BEGIN\\s+\\w+\\s+PRIVATE\\s+KEY-----)", 
            "私钥文件", "数据库配置", 5),
            
        new SensitivePattern("密码配置", 
            "(?i)(?:admin_?pass|password|[a-z]{3,15}_?password|user_?pass|user_?pwd|admin_?pwd)[\\s]*[:=][\\s]*[\"']?([a-z0-9!@#$%&*]{6,20})[\"']?", 
            "配置文件中的密码", "数据库配置", 5),

        // === Webhook URLs类 (高风险) ===
        new SensitivePattern("企业微信Webhook", 
            "\\b(https://qyapi\\.weixin\\.qq\\.com/cgi-bin/webhook/send\\?key=[a-zA-Z0-9\\-]{25,50})\\b", 
            "企业微信机器人Webhook", "Webhook URLs", 4),
            
        new SensitivePattern("钉钉Webhook", 
            "\\b(https://oapi\\.dingtalk\\.com/robot/send\\?access_token=[a-z0-9]{50,80})\\b", 
            "钉钉机器人Webhook", "Webhook URLs", 4),
            
        new SensitivePattern("飞书Webhook", 
            "\\b(https://open\\.feishu\\.cn/open-apis/bot/v2/hook/[a-z0-9\\-]{25,50})\\b", 
            "飞书机器人Webhook", "Webhook URLs", 4),
            
        new SensitivePattern("Slack Webhook", 
            "\\b(https://hooks\\.slack\\.com/services/[a-zA-Z0-9\\-_]{6,12}/[a-zA-Z0-9\\-_]{6,12}/[a-zA-Z0-9\\-_]{15,24})\\b", 
            "Slack机器人Webhook", "Webhook URLs", 4),

        // === 微信相关类 (中高风险) ===
        new SensitivePattern("微信AppID", 
            "\\b(wx[a-z0-9]{15,18})\\b", 
            "微信公众号/小程序AppID", "微信相关", 4),
            
        new SensitivePattern("企业微信CorpID", 
            "\\b(ww[a-z0-9]{15,18})\\b", 
            "企业微信CorpID", "微信相关", 4),

        // === 其他敏感信息类 ===
        new SensitivePattern("API路径", 
            "(?:[\"']|\\b)((?:/[a-zA-Z0-9._\\-~!*'();:@&=+$,/?#\\[\\]%]+){2,})(?:[\"']|\\b)", 
            "API路径或文件路径", "其他信息", 2),
            
        new SensitivePattern("加密算法", 
            "\\b(AES|DES|3DES|RSA|MD5|SHA1|SHA256|SHA512|HMAC|Base64|bcrypt|scrypt|PBKDF2|blowfish|twofish)\\b", 
            "加密算法标识", "其他信息", 3),
            
        new SensitivePattern("密钥配置", 
            "(?i)(?:secret|key|password|pwd|pass|token|auth|api_key|apikey|access_key|private_key|public_key)\\s*[:=]\\s*[\"']?([a-zA-Z0-9\\-_+/=]{8,})[\"']?", 
            "密钥和配置信息", "其他信息", 4)
    };
    
    /**
     * 获取所有分类
     */
    public static Map<String, List<String>> getCategories() {
        Map<String, List<String>> categories = new LinkedHashMap<>();
        
        for (SensitivePattern pattern : SENSITIVE_PATTERNS) {
            categories.computeIfAbsent(pattern.category, k -> new ArrayList<>()).add(pattern.name);
        }
        
        return categories;
    }
    
    /**
     * 获取指定分类的规则
     */
    public static List<SensitivePattern> getPatternsByCategory(String category) {
        List<SensitivePattern> result = new ArrayList<>();
        for (SensitivePattern pattern : SENSITIVE_PATTERNS) {
            if (pattern.category.equals(category)) {
                result.add(pattern);
            }
        }
        return result;
    }
    
    /**
     * 根据名称获取规则
     */
    public static SensitivePattern getPatternByName(String name) {
        for (SensitivePattern pattern : SENSITIVE_PATTERNS) {
            if (pattern.name.equals(name)) {
                return pattern;
            }
        }
        return null;
    }
    
    /**
     * 获取所有规则名称
     */
    public static String[] getAllPatternNames() {
        String[] names = new String[SENSITIVE_PATTERNS.length];
        for (int i = 0; i < SENSITIVE_PATTERNS.length; i++) {
            names[i] = SENSITIVE_PATTERNS[i].name;
        }
        return names;
    }
    
    /**
     * 根据风险等级获取描述
     */
    public static String getRiskDescription(int riskLevel) {
        switch (riskLevel) {
            case 5: return "极高风险";
            case 4: return "高风险";
            case 3: return "中高风险";
            case 2: return "中风险";
            case 1: return "低风险";
            default: return "未知风险";
        }
    }
    
    /**
     * 获取风险等级颜色
     */
    public static String getRiskColor(int riskLevel) {
        switch (riskLevel) {
            case 5: return "#FF0000"; // 红色
            case 4: return "#FF6600"; // 橙红
            case 3: return "#FF9900"; // 橙色
            case 2: return "#FFCC00"; // 黄色
            case 1: return "#99CC00"; // 绿色
            default: return "#CCCCCC"; // 灰色
        }
    }
} 