package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.*;
import java.util.List;
import java.net.URL;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

// Main Burp extension class that implements the required interfaces
public class BurpExtender implements IBurpExtender, ITab, IHttpListener {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private JPanel mainPanel;
    private JTabbedPane mainTabbedPane;
    private JTextArea outputArea;
    private JTextField urlField;
    private JTextArea customHeadersField;
    private JComboBox<String> attackTypeBox;
    private JComboBox<String> httpMethodBox;
    private JButton startButton;
    
    // Manual API testing controls
    private JTextField apiPathsField;
    private JButton testButton;
    
    // Domain filtering components
    private JTextField domainFilterField;
    private JCheckBox enableDomainFilterCheckBox;
    private JButton clearFilterButton;
    private Set<String> domainFilters = new HashSet<>();
    
    // Domain list components
    private DefaultListModel<String> domainListModel;
    private JList<String> domainList;
    private Set<String> discoveredDomains = new HashSet<>();
    private JLabel domainCountLabel;
    
    // 敏感信息提取功能已集成到API发现Tab中，移除独立组件
    
    // Data collections - 敏感信息相关数据已移至ApiDiscoveryTab
    private Set<String> apiPaths = new HashSet<>();
    private Set<String> jsUrls = new HashSet<>();

    // 被动分析组件
    private PassiveAnalysisConfig passiveConfig;
    private PassiveAnalysisProcessor passiveProcessor;
    private ApiScanMainTab apiScanMainTab;
    private PassiveAnalysisControlPanel passiveControlPanel;

    // Scan results storage for filtering
    private java.util.List<ScanResult> allScanResults = new java.util.ArrayList<>();
    
    // Scan result class
    private static class ScanResult {
        public final String type;
        public final String content;
        public final String sourceUrl;
        public final long timestamp;
        
        public ScanResult(String type, String content, String sourceUrl) {
            this.type = type;
            this.content = content;
            this.sourceUrl = sourceUrl;
            this.timestamp = System.currentTimeMillis();
        }
    }
    
    // 敏感信息提取相关常量已移至ApiDiscoveryTab中

    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();

        callbacks.setExtensionName("API Security Checker");
        callbacks.registerHttpListener(this);

        // 初始化被动分析组件
        initializePassiveAnalysis();

        // Create UI
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                createUI();
                callbacks.addSuiteTab(BurpExtender.this);
            }
        });

        callbacks.printOutput("API Security Checker extension loaded successfully!");
        callbacks.printOutput("被动分析功能已启用，将自动分析HTTP流量");
    }

    /**
     * 初始化被动分析组件
     */
    private void initializePassiveAnalysis() {
        try {
            // 创建被动分析配置
            passiveConfig = new PassiveAnalysisConfig();

            // 创建被动分析处理器
            passiveProcessor = new PassiveAnalysisProcessor(callbacks, passiveConfig);

            callbacks.printOutput("被动分析组件初始化成功");
        } catch (Exception e) {
            callbacks.printError("被动分析组件初始化失败: " + e.getMessage());
        }
    }

    @Override
    public String getTabCaption() {
        return "API Checker";
    }
    
    @Override
    public Component getUiComponent() {
        return mainPanel;
    }
    
    @Override
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        if (!messageIsRequest) {
            try {
                // 使用被动分析处理器处理HTTP消息
                if (passiveProcessor != null) {
                    passiveProcessor.processHttpMessage(toolFlag, messageIsRequest, messageInfo);
                }

                // 保留原有的域名过滤和收集逻辑作为备用
                IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
                URL url = requestInfo.getUrl();
                String urlString = url.toString();

                // Check domain filter
                if (!matchesDomainFilter(urlString)) {
                    return;
                }

                // Collect domains (备用逻辑)
                addDiscoveredDomain(urlString);

                // Look for JavaScript files (备用逻辑)
                if (urlString.endsWith(".js")) {
                    jsUrls.add(urlString);
                    addScanResult("JS Found", urlString, urlString);
                }

                // Look for API patterns in response (备用逻辑)
                byte[] response = messageInfo.getResponse();
                if (response != null) {
                    String responseString = new String(response);
                    findApiPaths(responseString, url.toString());
                }
            } catch (Exception e) {
                callbacks.printError("Error processing HTTP message: " + e.getMessage());
            }
        }
    }
    
    private void createUI() {
        mainPanel = new JPanel();
        mainPanel.setLayout(new BorderLayout());

        mainTabbedPane = new JTabbedPane();

        // 创建完整的API扫描主Tab（包含5个子Tab）
        // API发现功能已集成完整的敏感信息提取能力，无需单独的敏感信息提取Tab
        apiScanMainTab = new ApiScanMainTab(callbacks);
        mainTabbedPane.addTab("API扫描", apiScanMainTab.getUiComponent());

        // 创建被动分析控制面板
        if (passiveConfig != null && passiveProcessor != null) {
            passiveControlPanel = new PassiveAnalysisControlPanel(callbacks, passiveConfig, passiveProcessor);
            mainTabbedPane.addTab("被动分析", passiveControlPanel);
        }

        // 注册被动分析接收器
        if (passiveProcessor != null) {
            passiveProcessor.registerReceiver(apiScanMainTab);
            callbacks.printOutput("已注册被动分析接收器: " + apiScanMainTab.getReceiverName());
        }

        mainPanel.add(mainTabbedPane, BorderLayout.CENTER);

        callbacks.customizeUiComponent(mainPanel);
    }
    
    // createApiScanPanel方法已移除 - 功能已完全集成到ApiScanMainTab中
    
    // createSensitiveInfoPanel方法已移除 - 敏感信息提取功能已集成到API发现Tab中
    
    private JPanel createDomainListPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("发现的域名"));
        
        // Domain count label
        domainCountLabel = new JLabel("域名数量: 0");
        domainCountLabel.setHorizontalAlignment(SwingConstants.CENTER);
        panel.add(domainCountLabel, BorderLayout.NORTH);
        
        // Domain list
        domainListModel = new DefaultListModel<>();
        domainList = new JList<>(domainListModel);
        domainList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // Double click to filter
        domainList.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    String selectedDomain = domainList.getSelectedValue();
                    if (selectedDomain != null) {
                        applyDomainFilter(selectedDomain);
                    }
                }
            }
        });
        
        JScrollPane domainScrollPane = new JScrollPane(domainList);
        panel.add(domainScrollPane, BorderLayout.CENTER);
        
        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton filterButton = new JButton("筛选");
        filterButton.addActionListener(e -> {
            String selectedDomain = domainList.getSelectedValue();
            if (selectedDomain != null) {
                applyDomainFilter(selectedDomain);
            }
        });
        buttonPanel.add(filterButton);
        
        JButton clearFilterButton = new JButton("清除筛选");
        clearFilterButton.addActionListener(e -> clearDomainFilter());
        buttonPanel.add(clearFilterButton);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    // Core functionality methods
    private void updateDomainFilters() {
        domainFilters.clear();
        String filterText = domainFilterField.getText().trim();
        if (!filterText.isEmpty()) {
            String[] filters = filterText.split(",");
            for (String filter : filters) {
                String trimmedFilter = filter.trim();
                if (!trimmedFilter.isEmpty()) {
                    domainFilters.add(trimmedFilter.toLowerCase());
                }
            }
        }
    }
    
    private boolean matchesDomainFilter(String url) {
        // ✅ 添加null检查，避免NullPointerException
        if (enableDomainFilterCheckBox == null || !enableDomainFilterCheckBox.isSelected() || domainFilters.isEmpty()) {
            return true;
        }

        try {
            URL urlObj = new URL(url);
            String host = urlObj.getHost().toLowerCase();

            for (String filter : domainFilters) {
                if (matchesDomainPattern(host, filter)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    private boolean matchesDomainPattern(String domain, String pattern) {
        String regex = pattern.replace(".", "\\.").replace("*", ".*");
        try {
            return domain.matches(regex);
        } catch (Exception e) {
            return domain.contains(pattern.replace("*", ""));
        }
    }
    
    private void addDiscoveredDomain(String url) {
        try {
            URL urlObj = new URL(url);
            String domain = urlObj.getHost();
            if (domain != null && !domain.isEmpty()) {
                boolean isNew = discoveredDomains.add(domain);
                if (isNew) {
                    SwingUtilities.invokeLater(() -> {
                        domainListModel.addElement(domain);
                        domainCountLabel.setText("域名数量: " + discoveredDomains.size());
                    });
                }
            }
        } catch (Exception e) {
            // Ignore URL parsing errors
        }
    }
    
    private void applyDomainFilter(String domain) {
        // ✅ 添加null检查
        if (enableDomainFilterCheckBox != null) {
            enableDomainFilterCheckBox.setSelected(true);
        }
        if (domainFilterField != null) {
            domainFilterField.setEnabled(true);
            domainFilterField.setText(domain);
        }
        if (clearFilterButton != null) {
            clearFilterButton.setEnabled(true);
        }
        updateDomainFilters();
        filterAndDisplayResults();
        if (outputArea != null) {
            outputArea.append("已应用域名过滤: " + domain + "\n");
        }
    }
    
    private void clearDomainFilter() {
        // ✅ 添加null检查
        if (enableDomainFilterCheckBox != null) {
            enableDomainFilterCheckBox.setSelected(false);
        }
        if (domainFilterField != null) {
            domainFilterField.setEnabled(false);
            domainFilterField.setText("");
        }
        if (clearFilterButton != null) {
            clearFilterButton.setEnabled(false);
        }
        updateDomainFilters();
        filterAndDisplayResults();
        if (outputArea != null) {
            outputArea.append("已清除域名过滤\n");
        }
    }
    
    private void filterAndDisplayResults() {
        SwingUtilities.invokeLater(() -> {
            StringBuilder filteredOutput = new StringBuilder();
            filteredOutput.append("=== 筛选结果 ===\n");
            
            // ✅ 添加null检查
            if (enableDomainFilterCheckBox != null && enableDomainFilterCheckBox.isSelected() && !domainFilters.isEmpty()) {
                filteredOutput.append("过滤规则: ").append(String.join(", ", domainFilters)).append("\n");
            }
            
            // Filter JS files
            Set<String> filteredJsUrls = new HashSet<>();
            for (String jsUrl : jsUrls) {
                if (matchesDomainFilter(jsUrl)) {
                    filteredJsUrls.add(jsUrl);
                }
            }
            
            if (!filteredJsUrls.isEmpty()) {
                filteredOutput.append("\n筛选后的JavaScript文件 (").append(filteredJsUrls.size()).append("个):\n");
                for (String jsUrl : filteredJsUrls) {
                    filteredOutput.append("  - ").append(jsUrl).append("\n");
                }
            }
            
            // Filter API paths
            Set<String> filteredApiPaths = new HashSet<>();
            for (String apiPath : apiPaths) {
                String fullUrl = urlField.getText();
                if (fullUrl != null && !fullUrl.isEmpty()) {
                    try {
                        URL baseUrl = new URL(fullUrl);
                        String testUrl = baseUrl.getProtocol() + "://" + baseUrl.getHost() + apiPath;
                        if (matchesDomainFilter(testUrl)) {
                            filteredApiPaths.add(apiPath);
                        }
                    } catch (Exception e) {
                        filteredApiPaths.add(apiPath);
                    }
                }
            }
            
            if (!filteredApiPaths.isEmpty()) {
                filteredOutput.append("\n筛选后的API端点 (").append(filteredApiPaths.size()).append("个):\n");
                for (String apiPath : filteredApiPaths) {
                    filteredOutput.append("  - ").append(apiPath).append("\n");
                }
            }
            
            filteredOutput.append("\n统计: JS文件 ").append(filteredJsUrls.size())
                          .append("/").append(jsUrls.size())
                          .append(", API端点 ").append(filteredApiPaths.size())
                          .append("/").append(apiPaths.size()).append("\n");
            
            outputArea.setText(filteredOutput.toString());
        });
    }
    
    private void addScanResult(String type, String content, String sourceUrl) {
        allScanResults.add(new ScanResult(type, content, sourceUrl));
        
        // ✅ 添加null检查
        if (enableDomainFilterCheckBox != null && enableDomainFilterCheckBox.isSelected() && !matchesDomainFilter(sourceUrl)) {
            return;
        }
        
        SwingUtilities.invokeLater(() -> {
            outputArea.append("[" + type + "] " + content + "\n");
        });
    }
    
    private void clearAllResults() {
        allScanResults.clear();
        apiPaths.clear();
        jsUrls.clear();
        discoveredDomains.clear();
        
        SwingUtilities.invokeLater(() -> {
            outputArea.setText("");
            domainListModel.clear();
            domainCountLabel.setText("域名数量: 0");
        });
    }
    
    private void findApiPaths(String content, String sourceUrl) {
        // Simple API path patterns
        String[] patterns = {
            "[\"\\']/api/[a-zA-Z0-9_/\\-]*",
            "[\"\\']/v\\d+/[a-zA-Z0-9_/\\-]*",
            "fetch\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
            "axios\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']"
        };
        
        Set<String> foundPaths = new HashSet<>();
        
        for (String patternStr : patterns) {
            try {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(content);
                
                while (matcher.find()) {
                    String path = matcher.group();
                    if (matcher.groupCount() > 0 && matcher.group(1) != null) {
                        path = matcher.group(1);
                    }
                    
                    path = path.replaceAll("[\"\']", "").trim();
                    
                    if (path.startsWith("/") && path.length() > 1) {
                        foundPaths.add(path);
                    }
                }
            } catch (Exception e) {
                // Ignore pattern errors
            }
        }
        
        apiPaths.addAll(foundPaths);
        
        if (!foundPaths.isEmpty()) {
            addScanResult("API Paths", "Found " + foundPaths.size() + " paths", sourceUrl);
            for (String path : foundPaths) {
                addScanResult("API", path, sourceUrl);
            }
        }
    }
    
    // extractSensitiveInfo方法已移除 - 敏感信息提取功能已完全集成到API发现Tab中
    
    private void startScan() {
        String targetUrl = urlField.getText().trim();
        if (targetUrl.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "Please enter a target URL", "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        startButton.setEnabled(false);
        outputArea.append("Starting scan for: " + targetUrl + "\n");
        
        new Thread(() -> {
            try {
                performScan(targetUrl);
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    outputArea.append("Scan error: " + e.getMessage() + "\n");
                });
            } finally {
                SwingUtilities.invokeLater(() -> startButton.setEnabled(true));
            }
        }).start();
    }
    
    private void performScan(String targetUrl) {
        SwingUtilities.invokeLater(() -> outputArea.append("Analyzing target URL...\n"));
        
        try {
            URL url = new URL(targetUrl);
            IHttpService httpService = helpers.buildHttpService(url.getHost(), 
                url.getPort() == -1 ? (url.getProtocol().equals("https") ? 443 : 80) : url.getPort(), 
                url.getProtocol());
            
            byte[] request = helpers.buildHttpRequest(url);
            IHttpRequestResponse requestResponse = callbacks.makeHttpRequest(httpService, request);
            
            if (requestResponse.getResponse() != null) {
                String response = new String(requestResponse.getResponse());
                addDiscoveredDomain(targetUrl);
                
                // Find JavaScript files
                Set<String> foundJsUrls = findJavaScriptUrls(response, targetUrl);
                for (String jsUrl : foundJsUrls) {
                    jsUrls.add(jsUrl);
                    addDiscoveredDomain(jsUrl);
                    addScanResult("JS Found", jsUrl, jsUrl);
                }
                
                // Find API paths
                findApiPaths(response, targetUrl);
                
                // 敏感信息提取功能已移至API发现Tab中
                
                SwingUtilities.invokeLater(() -> {
                    outputArea.append("Scan completed!\n");
                    outputArea.append("Found " + jsUrls.size() + " JS files and " + apiPaths.size() + " API endpoints\n\n");
                });
            }
        } catch (Exception e) {
            SwingUtilities.invokeLater(() -> {
                outputArea.append("Error during scan: " + e.getMessage() + "\n");
            });
        }
    }
    
    private Set<String> findJavaScriptUrls(String html, String baseUrl) {
        Set<String> jsUrls = new HashSet<>();
        
        Pattern scriptPattern = Pattern.compile("<script[^>]+src=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher matcher = scriptPattern.matcher(html);
        
        while (matcher.find()) {
            String src = matcher.group(1);
            if (src.endsWith(".js")) {
                try {
                    if (src.startsWith("http")) {
                        jsUrls.add(src);
                    } else {
                        URL base = new URL(baseUrl);
                        URL jsUrl = new URL(base, src);
                        jsUrls.add(jsUrl.toString());
                    }
                } catch (Exception e) {
                    // Skip invalid URLs
                }
            }
        }
        
        return jsUrls;
    }
    
    // startSensitiveAnalysis方法已移除 - 敏感信息提取功能已集成到API发现Tab中

    // 移除复杂的敏感信息分析方法，还原到原始简单状态
    
    private void exportResults() {
        StringBuilder sb = new StringBuilder();
        sb.append("API Security Checker Results\n");
        sb.append("============================\n\n");
        
        sb.append("扫描时间: ").append(new java.util.Date()).append("\n");
        sb.append("目标URL: ").append(urlField.getText()).append("\n");
        
        // ✅ 添加null检查
        if (enableDomainFilterCheckBox != null && enableDomainFilterCheckBox.isSelected() && !domainFilters.isEmpty()) {
            sb.append("域名过滤: 已启用\n");
            sb.append("过滤规则: ").append(String.join(", ", domainFilters)).append("\n");
        } else {
            sb.append("域名过滤: 未启用\n");
        }
        sb.append("\n");
        
        sb.append("JavaScript Files Found:\n");
        for (String jsUrl : jsUrls) {
            sb.append("- ").append(jsUrl).append("\n");
        }
        
        sb.append("\nAPI Endpoints Found:\n");
        for (String apiPath : apiPaths) {
            sb.append("- ").append(apiPath).append("\n");
        }
        
        JTextArea textArea = new JTextArea(sb.toString());
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(600, 400));
        
        JOptionPane.showMessageDialog(mainPanel, scrollPane, "Export Results", JOptionPane.INFORMATION_MESSAGE);
    }

    // 敏感信息处理相关方法已移至ApiDiscoveryTab中

    // 移除复杂的导出和辅助方法，还原到原始简单状态
}