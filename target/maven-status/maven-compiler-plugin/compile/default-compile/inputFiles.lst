/Users/<USER>/work/jschecker/src/main/java/burp/SensitiveInfoConfig.java
/Users/<USER>/work/jschecker/src/main/java/burp/BurpExtender.java
/Users/<USER>/work/jschecker/src/main/java/burp/IPassiveAnalysisReceiver.java
/Users/<USER>/work/jschecker/src/main/java/burp/JsRouteScanStyleTestResultTab.java
/Users/<USER>/work/jschecker/src/main/java/burp/SensitiveInfoTab.java
/Users/<USER>/work/jschecker/src/main/java/burp/HostContent.java
/Users/<USER>/work/jschecker/src/main/java/burp/HostManagementTab.java
/Users/<USER>/work/jschecker/src/main/java/burp/HttpHandler.java
/Users/<USER>/work/jschecker/src/main/java/burp/EnhancedSensitiveInfoExtractor.java
/Users/<USER>/work/jschecker/src/main/java/burp/ApiTestResultTab.java
/Users/<USER>/work/jschecker/src/main/java/burp/IBurpExtender.java
/Users/<USER>/work/jschecker/src/main/java/burp/PassiveAnalysisProcessor.java
/Users/<USER>/work/jschecker/src/main/java/burp/ApiUtils.java
/Users/<USER>/work/jschecker/src/main/java/burp/PassiveAnalysisControlPanel.java
/Users/<USER>/work/jschecker/src/main/java/burp/RouteContent.java
/Users/<USER>/work/jschecker/src/main/java/burp/PassiveAnalysisConfig.java
/Users/<USER>/work/jschecker/src/main/java/burp/ApiScanMainTab.java
/Users/<USER>/work/jschecker/src/main/java/burp/SensitiveInfoExtractor.java
/Users/<USER>/work/jschecker/src/main/java/burp/ApiTestingTab.java
/Users/<USER>/work/jschecker/src/main/java/burp/PathDiscoveryTab.java
/Users/<USER>/work/jschecker/src/main/java/burp/ApiDiscoveryTab.java
