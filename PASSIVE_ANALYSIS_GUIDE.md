# 被动分析功能使用指南

## 概述

本插件现已集成完整的被动分析功能，能够自动分析通过Burp Suite的所有HTTP流量，实时发现API路径、敏感信息、JavaScript文件和域名信息。

## 功能特性

### 1. 自动被动分析
- **API路径发现**: 自动从HTTP响应中提取API端点
- **敏感信息提取**: 识别API密钥、Token、数据库连接字符串等敏感数据
- **JavaScript文件分析**: 发现和收集JavaScript文件URL
- **域名收集**: 自动收集访问的域名信息

### 2. 智能过滤
- **域名过滤**: 支持包含/排除域名列表
- **文件类型过滤**: 自动跳过图片、CSS等静态资源
- **URL去重**: 避免重复分析相同的URL
- **响应大小限制**: 跳过过大的响应以提高性能

### 3. 性能优化
- **异步处理**: 不阻塞Burp Suite主线程
- **并发控制**: 限制同时处理的请求数量
- **内存管理**: 自动清理过期的处理记录

## 使用方法

### 1. 启用被动分析
1. 在Burp Suite中加载插件
2. 切换到"API扫描"标签页
3. 被动分析功能默认自动启用

### 2. 配置被动分析
1. 切换到"被动分析"标签页
2. 根据需要调整以下设置：
   - **功能开关**: 控制各项分析功能的启用/禁用
   - **域名过滤**: 设置要包含或排除的域名
   - **性能配置**: 调整处理速度和资源使用

### 3. 查看分析结果
被动分析的结果会自动显示在相应的标签页中：
- **API路径**: 在"API发现"子标签页中查看
- **敏感信息**: 在"敏感信息"子标签页中查看
- **域名信息**: 在主界面的域名列表中查看

## 配置选项详解

### 功能开关
- **启用被动分析**: 总开关，控制所有被动分析功能
- **API路径发现**: 从响应中提取API端点
- **敏感信息提取**: 识别各类敏感数据
- **JavaScript文件分析**: 收集JS文件URL
- **域名收集**: 记录访问的域名

### 过滤配置
- **启用域名过滤**: 开启域名过滤功能
- **包含域名**: 只分析指定域名的流量（支持通配符）
- **排除域名**: 跳过指定域名的流量（支持通配符）
- **排除扩展名**: 跳过指定文件类型（默认跳过图片、CSS等）

### 性能配置
- **最大URL/分钟**: 限制每分钟处理的URL数量
- **最大响应大小**: 跳过超过指定大小的响应
- **异步处理**: 启用异步处理以提高性能

## 支持的敏感信息类型

被动分析现在使用统一的敏感信息配置，支持50+种敏感信息类型：

### 身份信息类（高风险）
- 身份证号码（中国大陆）
- 手机号码（中国大陆）
- 邮箱地址
- 银行卡号

### 网络信息类（中风险）
- IPv4地址
- IPv6地址
- 域名和URL
- 内网地址

### 认证凭证类（高风险）
- JWT Token
- Basic Auth
- Bearer Token
- API Key / Secret Key
- Access Token / Refresh Token

### 数据库连接类（高风险）
- JDBC连接字符串
- MongoDB连接字符串
- MySQL/PostgreSQL连接
- Redis连接信息

### 云服务凭证类（极高风险）
- AWS Access Key / Secret Key
- 阿里云Access Key
- 腾讯云Secret Key
- Azure连接字符串

### 密码配置类（高风险）
- 密码字段
- 私钥信息
- 证书内容

### 其他敏感信息
- API路径
- 加密算法标识
- 密钥配置信息

### 智能过滤
- 自动过滤示例数据（example.com、test.com等）
- 排除明显的占位符和演示数据
- 根据类型进行特殊验证（如JWT格式检查）
- 风险等级评估（1-5级）

## API路径匹配模式

被动分析现在使用与API扫描相同的完整匹配规则，包括30+种模式：

### 基础API路径模式
- `/api/` 路径和相关变体
- `/v1/`, `/v2/` 等版本化API
- `/rest/`, `/service/`, `/ajax/` 等服务路径
- 配置相关的URL和路径声明

### 增强模式（Chrome扩展规则）
- 完整路径模式：`'/path'` 格式
- 不完整路径模式：`'path/subpath'` 格式
- HTTP请求方法相关：`fetch()`, `axios()`, `$.ajax()` 等
- 现代前端框架：Vue、React、Angular等框架的HTTP调用

### 特殊匹配
- 路由配置模式：`path:`, `url:` 等配置声明
- HTML属性：`href`, `action` 属性中的路径
- 文件扩展名：`.json`, `.xml`, `.api` 等数据文件
- 完整URL：包含协议的完整HTTP/HTTPS地址

### 智能过滤
- 自动排除静态资源（图片、CSS、JS库等）
- 过滤明显的前端框架文件
- 排除包含中文字符的路径
- 长度和格式验证

## 统计信息

被动分析控制面板提供实时统计信息：
- **总处理数**: 已处理的HTTP响应总数
- **API路径**: 发现的API路径数量
- **敏感信息**: 发现的敏感信息条目数
- **JS文件**: 发现的JavaScript文件数量
- **已处理URL**: 去重后的URL数量

## 最佳实践

### 1. 性能优化
- 根据目标应用调整域名过滤，避免分析无关流量
- 适当设置响应大小限制，跳过大文件
- 在高流量环境中降低最大URL/分钟限制

### 2. 结果管理
- 定期查看和导出分析结果
- 使用统计信息监控分析效果
- 根据需要重置统计数据

### 3. 安全考虑
- 敏感信息在日志中会被部分掩码显示
- 建议在安全环境中使用插件
- 定期清理已处理URL列表以释放内存

## 故障排除

### 常见问题
1. **被动分析不工作**
   - 检查全局开关是否启用
   - 确认域名过滤设置是否正确
   - 查看Burp Suite输出面板的错误信息

2. **发现的结果太少**
   - 检查过滤配置是否过于严格
   - 确认目标应用是否有相关内容
   - 调整敏感信息匹配模式

3. **性能问题**
   - 启用异步处理
   - 降低最大URL/分钟限制
   - 增加响应大小限制

### 调试信息
插件会在Burp Suite的输出面板中显示详细的调试信息：
- 被动分析状态变更
- 发现的内容摘要
- 错误和警告信息
- 统计数据更新

## 技术实现

### 架构设计
- **PassiveAnalysisConfig**: 配置管理
- **PassiveAnalysisProcessor**: 核心处理逻辑
- **IPassiveAnalysisReceiver**: 结果接收接口
- **PassiveAnalysisControlPanel**: 用户界面

### 处理流程
1. HTTP响应到达 → BurpExtender.processHttpMessage()
2. 检查配置和过滤条件
3. 异步/同步处理响应内容
4. 提取各类信息（API、敏感信息、JS文件等）
5. 通知注册的接收器
6. 更新统计信息和UI

### 扩展性
插件设计支持轻松扩展：
- 添加新的敏感信息匹配模式
- 实现自定义的分析接收器
- 扩展过滤和配置选项

## 更新日志

### v1.1 (当前版本)
- **统一提取规则**: 被动分析现在使用与API扫描相同的完整提取规则
- **增强API路径发现**: 支持30+种API路径匹配模式，包括Chrome扩展规则
- **统一敏感信息检测**: 使用SensitiveInfoConfig统一配置，支持50+种敏感信息类型
- **改进过滤逻辑**: 更智能的示例数据过滤和路径验证
- **性能优化**: 优化正则表达式匹配和内存使用

### v1.0
- 实现完整的被动分析功能
- 支持API路径、敏感信息、JS文件和域名发现
- 提供丰富的配置选项和统计信息
- 集成到现有的API扫描界面中
- 优化性能和内存使用

---

如有问题或建议，请查看插件输出日志或联系开发团队。
